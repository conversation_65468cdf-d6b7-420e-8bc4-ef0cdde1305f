import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import threading
import queue
import time
from dm_main import GameScript
import pythoncom
from mq_listener import M<PERSON>istener  # 导入MQListener
import keyboard  # 添加到文件开头的导入部分
import pika


class LogRedirector:
    """日志重定向器，将输出重定向到GUI"""
    def __init__(self, gui):
        self.gui = gui

    def write(self, message):
        if message.strip():  # 忽略空行
            self.gui.log(message.strip())

    def flush(self):
        pass


class DeliveryGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏发货工具")

        # 添加temp_config初始化
        self.temp_config = {}

        # 添加在初始化部分
        self.setup_emergency_stop()

        # self.root.geometry("400x300")  # 缩小窗口尺寸
        self.root.minsize(width=400, height=260)  # 增加高度以适应新按钮

        self.root.attributes('-topmost', True)  # 窗口置顶

        # 获取屏幕宽度和窗口宽度
        screen_width = self.root.winfo_screenwidth()
        window_width = 400

        # 设置窗口位置到右上角
        x = screen_width - window_width
        self.root.geometry(f"{window_width}x180+{x}+0")
        
        # 设置配置文件路径
        self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'account_config.json')
        
        # 加载账号配置
        self.account_config = self.load_account_config()
        
        # 创建GameScript实例并传入stop_flag
        self.stop_flag = threading.Event()
        self.script = GameScript(stop_event=self.stop_flag)  # 初始化GameScript实例

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")  # 减小padding
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=2)  # 减小间距
        
        # 创建输入区域
        input_frame = ttk.Frame(control_frame)
        input_frame.pack(fill=tk.X, pady=2)
        
        # 区服选择
        ttk.Label(input_frame, text="区服:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.region_var = tk.StringVar()
        self.region_combo = ttk.Combobox(input_frame, textvariable=self.region_var, width=15)
        self.region_combo['values'] = ['梦江南','乾坤一掷','唯我独尊','天鹅坪','破阵子','剑胆琴心','幽月轮','斗转星移','绝代天骄']
        self.region_combo.grid(row=0, column=1, sticky=tk.W, pady=2)
        self.region_combo.set('梦江南')
        
        # 帮会选择
        ttk.Label(input_frame, text="帮会:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.guild_var = tk.StringVar()
        self.guild_combo = ttk.Combobox(input_frame, textvariable=self.guild_var, width=15)
        self.guild_combo['values'] = ['帮1', '帮2', '帮3', '帮4', '帮5', '帮6', '帮7', '帮8', '帮9']
        self.guild_combo.grid(row=1, column=1, sticky=tk.W, pady=2)
        self.guild_combo.set('帮1')
        
        # 外观输入
        ttk.Label(input_frame, text="外观:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.appearance_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.appearance_var, width=17).grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # 角色名称输入
        ttk.Label(input_frame, text="角色:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.character_name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.character_name_var, width=17).grid(row=3, column=1, sticky=tk.W, pady=2)

        # 订单ID输入
        ttk.Label(input_frame, text="订单ID:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.order_id_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.order_id_var, width=17).grid(row=4, column=1, sticky=tk.W, pady=2)

        # 数量输入
        ttk.Label(input_frame, text="数量:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.quantity_var = tk.StringVar()
        self.quantity_var.set("1")  # 默认数量为1
        quantity_entry = ttk.Entry(input_frame, textvariable=self.quantity_var, width=17)
        quantity_entry.grid(row=5, column=1, sticky=tk.W, pady=2)

        # 绑定数量变化事件，动态更新按钮文字
        self.quantity_var.trace('w', self.update_button_text)

        # 添加数量说明
        ttk.Label(input_frame, text="(相同外观的数量)", font=('Arial', 8), foreground='gray').grid(row=6, column=1, sticky=tk.W, pady=1)
        
        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=2)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=1)

        self.config_button = ttk.Button(button_row1, text="账号配置", command=self.show_account_config, width=8)
        self.config_button.pack(side=tk.LEFT, padx=2)

        self.start_button = ttk.Button(button_row1, text="开始发货", command=self.start_delivery, width=8)
        self.start_button.pack(side=tk.LEFT, padx=2)

        self.stop_button = ttk.Button(button_row1, text="停止", command=self.stop_delivery, state=tk.DISABLED, width=8)
        self.stop_button.pack(side=tk.LEFT, padx=2)

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X, pady=1)

        self.direct_delivery_button = ttk.Button(button_row2, text="直接发货", command=self.start_direct_delivery, width=12)
        self.direct_delivery_button.pack(side=tk.LEFT, padx=2)

        # 添加提示标签
        ttk.Label(button_row2, text="(跳过登录)", font=('Arial', 8), foreground='gray').pack(side=tk.LEFT, padx=2)
        
        # 右侧日志区域
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=2)
        
        # 日志显示（减小高度）
        self.log_text = scrolledtext.ScrolledText(log_frame, width=20, height=5)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=1, column=0, columnspan=2, pady=2)
        
        # 初始化任务相关变量
        self.task_thread = None
        self.stop_flag = threading.Event()  # 使用Event来控制停止
        self.log_queue = queue.Queue()
        
        # 启动日志更新
        self.root.after(100, self.update_log)

        # 启动MQ监听
        self.start_mq_listener()

        # 初始化按钮文字
        self.update_button_text()

    def update_button_text(self, *args):
        """根据数量动态更新按钮文字"""
        try:
            quantity_str = self.quantity_var.get()
            quantity = int(quantity_str) if quantity_str and quantity_str.isdigit() else 1

            if quantity <= 1:
                # 数量为1时显示原始文字
                self.start_button.config(text="开始发货")
                self.direct_delivery_button.config(text="直接发货")
            else:
                # 数量大于1时显示数量
                self.start_button.config(text=f"发货{quantity}个")
                self.direct_delivery_button.config(text=f"直接发货{quantity}个")
        except:
            # 如果出现异常，使用默认文字
            self.start_button.config(text="开始发货")
            self.direct_delivery_button.config(text="直接发货")

    def start_mq_listener(self):
        """启动MQ监听"""
        print("启动MQ监听...")
        if self.script is None:
            print("GameScript实例未初始化，无法启动MQ监听")
            return

        def run_listener():
            listener = MQListener(self.script)  # 创建MQListener实例
            if not listener.connect():  # 确保连接成功
                print("MQListener连接失败，无法开始监听")
                return
            listener.start_listening(self.handle_game_message)  # 监听消息

        threading.Thread(target=run_listener, daemon=True).start()

    def handle_game_message(self, message):
        """处理接收到的游戏消息"""
        self.region_var.set(message.region)
        self.guild_var.set(message.guild)
        self.appearance_var.set(message.appearance)
        self.character_name_var.set(message.character_name)
        self.order_id_var.set(message.order_id)

        # 自动开始发货
        self.start_delivery()
    
    def log(self, message):
        """添加日志到队列"""
        self.log_queue.put(f"[{time.strftime('%H:%M:%S')}] {message}\n")
    
    def update_log(self):
        """更新日志显示"""
        while not self.log_queue.empty():
            message = self.log_queue.get()
            self.log_text.insert(tk.END, message)
            self.log_text.see(tk.END)
        self.root.after(100, self.update_log)
    
    def load_account_config(self):
        """加载账号配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
        return {f"帮{i}": {"account": "", "password": ""} for i in range(1, 10)}
    
    def save_account_config(self):
        """保存账号配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.account_config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            return False
    
    def show_account_config(self):
        """显示账号配置窗口"""
        config_window = tk.Toplevel(self.root)
        config_window.title("账号配置")
        config_window.geometry("300x400")
        config_window.attributes('-topmost', True)
        
        # 设置配置窗口位置到右上角
        screen_width = config_window.winfo_screenwidth()
        window_width = 250
        x = screen_width - window_width
        config_window.geometry(f"{window_width}x800+{x}+0")
        
        # 创建带滚动条的画布
        canvas = tk.Canvas(config_window)
        scrollbar = ttk.Scrollbar(config_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置画布滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        scrollable_frame_id = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        # 当画布大小改变时，调整scrollable_frame的宽度
        def on_canvas_configure(event):
            canvas.itemconfig(scrollable_frame_id, width=event.width)
    
        canvas.bind("<Configure>", on_canvas_configure)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 为每个帮会创建账号密码输入框
        for guild in [f"帮{i}" for i in range(1, 10)]:
            frame = ttk.LabelFrame(scrollable_frame, text=guild, padding="2")
            frame.pack(fill=tk.X, padx=2, pady=1)
            
            # 账号输入框
            f1 = ttk.Frame(frame)
            f1.pack(fill=tk.X, pady=1)
            ttk.Label(f1, text="账号:", width=5).pack(side=tk.LEFT)
            account_var = tk.StringVar(value=self.account_config[guild]["account"])
            ttk.Entry(f1, textvariable=account_var, width=25).pack(side=tk.LEFT, padx=2)
            
            # 密码输入框
            f2 = ttk.Frame(frame)
            f2.pack(fill=tk.X, pady=1)
            ttk.Label(f2, text="密码:", width=5).pack(side=tk.LEFT)
            password_var = tk.StringVar(value=self.account_config[guild]["password"])
            ttk.Entry(f2, textvariable=password_var, show="*", width=25).pack(side=tk.LEFT, padx=2)
            
            self.temp_config[guild] = {
                "account": account_var,
                "password": password_var
            }
        
        # 保存按钮
        def save_config():
            try:
                for guild in self.temp_config:
                    self.account_config[guild] = {
                        "account": self.temp_config[guild]["account"].get(),
                        "password": self.temp_config[guild]["password"].get()
                    }
                if self.save_account_config():
                    messagebox.showinfo("提示", "配置已保存")
                    config_window.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
        
        save_button = ttk.Button(config_window, text="保存", command=save_config)
        save_button.pack(side=tk.BOTTOM, pady=2)
        
        # 放置画布和滚动条
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def start_delivery(self):
        """开始发货（异步）"""
        # 获取当前选择的值
        region = self.region_var.get()
        guild = self.guild_var.get()
        appearance = self.appearance_var.get()
        character_name = self.character_name_var.get()
        order_id = self.order_id_var.get()
        quantity_str = self.quantity_var.get()

        # 检查输入
        if not all([region, guild, appearance, character_name]):
            messagebox.showerror("错误", "请填写所有必要信息")
            return

        # 验证数量
        try:
            quantity = int(quantity_str) if quantity_str else 1
            if quantity <= 0:
                messagebox.showerror("错误", "数量必须大于0")
                return
            if quantity > 99:
                messagebox.showerror("错误", "数量不能超过99")
                return
        except ValueError:
            messagebox.showerror("错误", "数量必须是有效的数字")
            return

        # 多数量确认
        if quantity > 1:
            result = messagebox.askyesno("确认",
                f"您要发货 {quantity} 个 '{appearance}' 给 '{character_name}'，这将需要较长时间，确定继续吗？")
            if not result:
                return

        # 获取对应帮会的账号密码
        account_info = self.account_config.get(guild)
        if not account_info or not account_info["account"] or not account_info["password"]:
            messagebox.showerror("错误", f"请先配置{guild}的账号密码")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.config_button.configure(state=tk.DISABLED)
        
        # 重置停止标志
        self.stop_flag.clear()
        
        # 创建GameMessage对象
        class GameMessage:
            def __init__(self, account, password, region, guild, appearance, character_name, order_id):
                self.account = account
                self.password = password
                self.region = region
                self.guild = guild
                self.appearance = appearance
                self.character_name = character_name
                self.order_id = order_id
        # 创建消息对象
        message = GameMessage(
            account_info["account"],
            account_info["password"],
            region,
            guild,
            appearance,
            character_name,
            order_id
        )
        
        # 重定向GameScript的输出
        
        # 创建并启动任务线程
        def run_task():
            try:
                pythoncom.CoInitialize()
                
                import sys
                old_stdout = sys.stdout
                sys.stdout = LogRedirector(self)
                
                # 根据数量决定执行方式
                if quantity <= 1:
                    self.status_var.set("正在执行发货任务...")
                    self.log(f"🚀 开始发货: {appearance} -> {character_name}")
                    self.script.handle_game_message(message)
                else:
                    self.status_var.set("正在执行多数量发货任务...")
                    self.log(f"🚀 开始多数量发货: {appearance} x{quantity} -> {character_name}")
                    result = self.script.execute_multiple_delivery(appearance, character_name, quantity, order_id)
                    if not result:
                        self.status_var.set("多数量发货任务失败")
                        return

                if self.stop_flag.is_set():
                    self.status_var.set("任务已停止")
                    self.log("❌ 任务被用户停止")
                else:
                    if quantity <= 1:
                        self.status_var.set("发货任务完成")
                        self.log("✅ 发货任务完成")
                    else:
                        self.status_var.set("多数量发货任务完成")
                        self.log("✅ 多数量发货任务完成")
                
                # 确认消息
                try:
                    if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                        # 检查通道是否仍然打开
                        if not self.script.mq_channel.is_open:
                            self.log("MQ通道已关闭，无法确认消息")
                            return
                            
                        try:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认")
                        except pika.exceptions.ChannelClosedByBroker as e:
                            self.log(f"MQ通道已关闭，无法确认消息: {str(e)}")
                        except pika.exceptions.ChannelWrongStateError as e:
                            self.log(f"MQ通道状态错误，无法确认消息: {str(e)}")
                        except Exception as e:
                            self.log(f"消息确认失败: {str(e)}")
                    else:
                        self.log("无法确认消息：缺少mq_channel或mq_delivery_tag")
                except Exception as e:
                    self.log(f"消息确认过程发生错误: {str(e)}")
                    
                sys.stdout = old_stdout
                
            except Exception as e:
                self.log(f"发货失败: {str(e)}")
                self.status_var.set(f"发货失败: {str(e)}")
                # 在失败时也尝试确认消息
                if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                    try:
                        if self.script.mq_channel.is_open:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认（失败后）")
                    except Exception as e:
                        self.log(f"消息确认失败: {str(e)}")
            finally:
                pythoncom.CoUninitialize()
                self.root.after(0, self.reset_buttons)
        
        self.task_thread = threading.Thread(target=run_task)
        self.task_thread.daemon = True
        self.task_thread.start()

    def start_direct_delivery(self):
        """开始直接发货（跳过登录）"""
        # 获取当前选择的值
        appearance = self.appearance_var.get()
        character_name = self.character_name_var.get()
        order_id = self.order_id_var.get()
        quantity_str = self.quantity_var.get()

        # 检查输入
        if not all([appearance, character_name]):
            messagebox.showerror("错误", "请填写外观和角色名称")
            return

        # 验证数量
        try:
            quantity = int(quantity_str) if quantity_str else 1
            if quantity <= 0:
                messagebox.showerror("错误", "数量必须大于0")
                return
            if quantity > 99:
                messagebox.showerror("错误", "数量不能超过99")
                return
        except ValueError:
            messagebox.showerror("错误", "数量必须是有效的数字")
            return

        # 多数量确认
        if quantity > 1:
            result = messagebox.askyesno("确认",
                f"您要直接发货 {quantity} 个 '{appearance}' 给 '{character_name}'，这将需要较长时间，确定继续吗？")
            if not result:
                return

        # 禁用按钮
        self.start_button.configure(state=tk.DISABLED)
        self.direct_delivery_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.config_button.configure(state=tk.DISABLED)

        # 重置停止标志
        self.stop_flag.clear()

        # 重定向GameScript的输出

        # 创建并启动任务线程
        def run_direct_task():
            try:
                pythoncom.CoInitialize()

                import sys
                old_stdout = sys.stdout
                sys.stdout = LogRedirector(self)

                # 根据数量决定执行方式
                if quantity <= 1:
                    self.status_var.set("正在执行直接发货任务...")
                    self.log(f"🚀 开始直接发货: {appearance} -> {character_name}")
                    result = self.script.execute_direct_delivery(appearance, character_name, order_id)
                else:
                    self.status_var.set("正在执行多数量直接发货任务...")
                    self.log(f"🚀 开始多数量直接发货: {appearance} x{quantity} -> {character_name}")
                    result = self.script.execute_multiple_delivery(appearance, character_name, quantity, order_id)

                if self.stop_flag.is_set():
                    self.status_var.set("任务已停止")
                    self.log("❌ 任务被用户停止")
                elif result:
                    if quantity <= 1:
                        self.status_var.set("直接发货任务完成")
                        self.log("✅ 直接发货任务完成")
                    else:
                        self.status_var.set("多数量直接发货任务完成")
                        self.log("✅ 多数量直接发货任务完成")
                else:
                    if quantity <= 1:
                        self.status_var.set("直接发货任务失败")
                        self.log("❌ 直接发货任务失败")
                    else:
                        self.status_var.set("多数量直接发货任务失败")
                        self.log("❌ 多数量直接发货任务失败")

                sys.stdout = old_stdout

            except Exception as e:
                self.log(f"❌ 直接发货失败: {str(e)}")
                self.status_var.set(f"直接发货失败: {str(e)}")
            finally:
                pythoncom.CoUninitialize()
                self.root.after(0, self.reset_buttons)

        self.task_thread = threading.Thread(target=run_direct_task)
        self.task_thread.daemon = True
        self.task_thread.start()

    def stop_delivery(self):
        """停止发货任务"""
        if self.script:
            self.stop_flag.set()  # 设置停止标志
            self.log("正在停止任务...")
            self.status_var.set("正在停止任务...")

    def reset_buttons(self):
        """重置按钮状态"""
        self.start_button.configure(state=tk.NORMAL)
        self.direct_delivery_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        self.config_button.configure(state=tk.NORMAL)

    def setup_emergency_stop(self):
        """设置紧急停止热键"""
        keyboard.on_press_key("F12", self.emergency_stop)
        # 添加提示标签
        emergency_label = ttk.Label(
            self.root, 
            text="按 F12 紧急停止",
            foreground="red"
        )
        emergency_label.grid(row=2, column=0, columnspan=2, pady=2)

    def emergency_stop(self, e):
        """紧急停止处理函数"""
        try:
            print("触发紧急停止！")
            if self.script:
                self.stop_flag.set()  # 设置停止标志
                self.script.cleanup()  # 清理资源
            self.root.after(0, self.root.destroy)  # 安全地关闭窗口
            os._exit(0)  # 强制终止程序
        except Exception as e:
            print(f"紧急停止时发生错误: {str(e)}")
            os._exit(1)
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.emergency_stop(None)
        except Exception as e:
            print(f"程序异常: {str(e)}")
            self.emergency_stop(None)

if __name__ == "__main__":
    gui = DeliveryGUI()
    gui.run() 