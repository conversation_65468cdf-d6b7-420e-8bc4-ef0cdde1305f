#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI识别快速配置脚本
"""

import json
import os
import subprocess
import sys

def check_ollama_installed():
    """检查Ollama是否已安装"""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def check_ollama_running():
    """检查Ollama服务是否运行"""
    try:
        import requests
        response = requests.get('http://localhost:11434', timeout=3)
        return response.status_code == 200
    except:
        return False

def check_llava_model():
    """检查llava模型是否已下载"""
    try:
        result = subprocess.run(['ollama', 'list'], 
                              capture_output=True, text=True, timeout=10)
        return 'llava' in result.stdout
    except:
        return False

def setup_local_ai():
    """设置本地AI"""
    print("🦙 设置本地AI (Ollama + LLaVA)")
    print("=" * 40)
    
    # 检查Ollama安装
    if not check_ollama_installed():
        print("❌ Ollama未安装")
        print("请访问 https://ollama.ai/ 下载安装Ollama")
        return False
    
    print("✅ Ollama已安装")
    
    # 检查服务运行
    if not check_ollama_running():
        print("⚠️ Ollama服务未运行")
        print("请在新的命令行窗口运行: ollama serve")
        
        input("启动Ollama服务后，按回车继续...")
        
        if not check_ollama_running():
            print("❌ 仍无法连接到Ollama服务")
            return False
    
    print("✅ Ollama服务正在运行")
    
    # 检查模型
    if not check_llava_model():
        print("📥 正在下载LLaVA模型...")
        print("注意: 这将下载约4GB的数据，请确保网络连接稳定")
        
        try:
            result = subprocess.run(['ollama', 'pull', 'llava'], 
                                  timeout=1800)  # 30分钟超时
            if result.returncode != 0:
                print("❌ 模型下载失败")
                return False
        except subprocess.TimeoutExpired:
            print("❌ 模型下载超时")
            return False
        except Exception as e:
            print(f"❌ 模型下载出错: {e}")
            return False
    
    print("✅ LLaVA模型已准备就绪")
    return True

def setup_openai_api():
    """设置OpenAI API"""
    print("🤖 设置OpenAI API")
    print("=" * 40)
    
    api_key = input("请输入OpenAI API密钥 (sk-...): ").strip()
    
    if not api_key.startswith('sk-'):
        print("❌ API密钥格式不正确")
        return False, None
    
    # 测试API密钥
    try:
        import requests
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 简单的API测试
        response = requests.get(
            "https://api.openai.com/v1/models",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ API密钥验证成功")
            return True, api_key
        else:
            print(f"❌ API密钥验证失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False, None

def update_config(service_type, api_key=None):
    """更新配置文件"""
    try:
        with open('ai_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if service_type == 'local':
            config['services']['local']['enabled'] = True
            config['default_service'] = 'local'
        elif service_type == 'openai':
            config['services']['openai']['enabled'] = True
            config['services']['openai']['api_key'] = api_key
            config['default_service'] = 'openai'
        
        with open('ai_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")
        return False

def test_ai_setup():
    """测试AI设置"""
    print("\n🧪 测试AI设置...")
    
    try:
        from ai_ocr import get_ai_recognizer
        
        recognizer = get_ai_recognizer()
        results = recognizer.test_services()
        
        available = [k for k, v in results.items() if v]
        
        if available:
            print(f"✅ 可用的AI服务: {', '.join(available)}")
            return True
        else:
            print("❌ 没有可用的AI服务")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("⚡ AI识别快速配置")
    print("=" * 30)
    
    if not os.path.exists('ai_config.json'):
        print("❌ 配置文件不存在，请先运行 python test_ai_recognition.py")
        return
    
    print("选择AI服务:")
    print("1. 本地模型 (Ollama + LLaVA) - 免费，需要下载")
    print("2. OpenAI GPT-4V - 付费，识别准确")
    print("3. 跳过配置，使用传统OCR")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == '1':
        if setup_local_ai():
            if update_config('local'):
                print("\n🎉 本地AI配置成功！")
                test_ai_setup()
            else:
                print("\n❌ 配置失败")
        else:
            print("\n❌ 本地AI设置失败")
            
    elif choice == '2':
        success, api_key = setup_openai_api()
        if success:
            if update_config('openai', api_key):
                print("\n🎉 OpenAI API配置成功！")
                test_ai_setup()
            else:
                print("\n❌ 配置失败")
        else:
            print("\n❌ OpenAI API设置失败")
            
    elif choice == '3':
        print("\n✅ 将使用传统OCR识别")
        print("AI识别功能已集成，随时可以配置启用")
        
    else:
        print("\n❌ 无效选择")
        return
    
    print("\n📋 配置完成！")
    print("现在可以运行主程序，外观识别将使用AI模型")
    print("如果AI识别失败，会自动回退到传统OCR")

if __name__ == "__main__":
    main()
