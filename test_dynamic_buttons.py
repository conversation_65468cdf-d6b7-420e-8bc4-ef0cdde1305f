#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态按钮功能测试脚本
"""

import tkinter as tk
from tkinter import ttk

def test_button_text_logic():
    """测试按钮文字更新逻辑"""
    print("🧪 按钮文字更新逻辑测试")
    print("=" * 40)
    
    test_cases = [
        {"quantity": "", "expected_start": "开始发货", "expected_direct": "直接发货", "description": "空值"},
        {"quantity": "1", "expected_start": "开始发货", "expected_direct": "直接发货", "description": "数量1"},
        {"quantity": "2", "expected_start": "发货2个", "expected_direct": "直接发货2个", "description": "数量2"},
        {"quantity": "5", "expected_start": "发货5个", "expected_direct": "直接发货5个", "description": "数量5"},
        {"quantity": "10", "expected_start": "发货10个", "expected_direct": "直接发货10个", "description": "数量10"},
        {"quantity": "99", "expected_start": "发货99个", "expected_direct": "直接发货99个", "description": "最大数量99"},
        {"quantity": "abc", "expected_start": "开始发货", "expected_direct": "直接发货", "description": "非数字"},
        {"quantity": "0", "expected_start": "开始发货", "expected_direct": "直接发货", "description": "数量0"},
    ]
    
    for case in test_cases:
        quantity_str = case["quantity"]
        expected_start = case["expected_start"]
        expected_direct = case["expected_direct"]
        description = case["description"]
        
        print(f"\n📋 测试场景: {description}")
        print(f"   输入数量: '{quantity_str}'")
        
        # 模拟按钮文字更新逻辑
        try:
            quantity = int(quantity_str) if quantity_str and quantity_str.isdigit() else 1
            
            if quantity <= 1:
                actual_start = "开始发货"
                actual_direct = "直接发货"
            else:
                actual_start = f"发货{quantity}个"
                actual_direct = f"直接发货{quantity}个"
        except:
            actual_start = "开始发货"
            actual_direct = "直接发货"
        
        # 验证结果
        start_correct = actual_start == expected_start
        direct_correct = actual_direct == expected_direct
        
        print(f"   开始发货按钮: '{actual_start}' {'✅' if start_correct else '❌'}")
        print(f"   直接发货按钮: '{actual_direct}' {'✅' if direct_correct else '❌'}")
        
        if start_correct and direct_correct:
            print(f"   ✅ 测试通过")
        else:
            print(f"   ❌ 测试失败")

def test_quantity_validation():
    """测试数量验证逻辑"""
    print("\n🔢 数量验证逻辑测试")
    print("=" * 30)
    
    validation_cases = [
        {"input": "1", "valid": True, "description": "正常数量1"},
        {"input": "5", "valid": True, "description": "正常数量5"},
        {"input": "99", "valid": True, "description": "最大数量99"},
        {"input": "0", "valid": False, "description": "无效数量0"},
        {"input": "-1", "valid": False, "description": "负数"},
        {"input": "100", "valid": False, "description": "超过最大值"},
        {"input": "abc", "valid": False, "description": "非数字"},
        {"input": "", "valid": True, "description": "空值(默认1)"},
        {"input": "1.5", "valid": False, "description": "小数"},
    ]
    
    for case in validation_cases:
        input_val = case["input"]
        expected_valid = case["valid"]
        description = case["description"]
        
        print(f"\n📋 验证场景: {description}")
        print(f"   输入: '{input_val}'")
        
        # 模拟验证逻辑
        try:
            quantity = int(input_val) if input_val else 1
            actual_valid = 1 <= quantity <= 99
        except ValueError:
            actual_valid = False
        
        if actual_valid == expected_valid:
            print(f"   ✅ 验证通过: {'有效' if actual_valid else '无效'}")
        else:
            print(f"   ❌ 验证失败: 期望{'有效' if expected_valid else '无效'}，实际{'有效' if actual_valid else '无效'}")

def test_confirmation_logic():
    """测试确认对话框逻辑"""
    print("\n💬 确认对话框逻辑测试")
    print("=" * 30)
    
    confirmation_cases = [
        {"quantity": 1, "should_confirm": False, "description": "单个发货"},
        {"quantity": 2, "should_confirm": True, "description": "2个发货"},
        {"quantity": 5, "should_confirm": True, "description": "5个发货"},
        {"quantity": 10, "should_confirm": True, "description": "10个发货"},
    ]
    
    for case in confirmation_cases:
        quantity = case["quantity"]
        expected_confirm = case["should_confirm"]
        description = case["description"]
        
        print(f"\n📋 确认场景: {description}")
        print(f"   数量: {quantity}")
        
        # 模拟确认逻辑
        actual_confirm = quantity > 1
        
        if actual_confirm == expected_confirm:
            print(f"   ✅ 逻辑正确: {'需要确认' if actual_confirm else '无需确认'}")
        else:
            print(f"   ❌ 逻辑错误: 期望{'需要确认' if expected_confirm else '无需确认'}，实际{'需要确认' if actual_confirm else '无需确认'}")

def create_demo_gui():
    """创建演示GUI"""
    print("\n🖥️ 创建演示GUI")
    print("=" * 20)
    
    root = tk.Tk()
    root.title("动态按钮演示")
    root.geometry("400x200")
    
    # 数量输入
    frame = ttk.Frame(root)
    frame.pack(padx=20, pady=20)
    
    ttk.Label(frame, text="数量:").grid(row=0, column=0, sticky=tk.W, pady=5)
    quantity_var = tk.StringVar()
    quantity_var.set("1")
    quantity_entry = ttk.Entry(frame, textvariable=quantity_var, width=10)
    quantity_entry.grid(row=0, column=1, padx=10, pady=5)
    
    # 按钮
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=1, column=0, columnspan=2, pady=20)
    
    start_button = ttk.Button(button_frame, text="开始发货", width=12)
    start_button.pack(side=tk.LEFT, padx=5)
    
    direct_button = ttk.Button(button_frame, text="直接发货", width=12)
    direct_button.pack(side=tk.LEFT, padx=5)
    
    # 状态标签
    status_label = ttk.Label(frame, text="输入数量查看按钮变化", foreground="blue")
    status_label.grid(row=2, column=0, columnspan=2, pady=10)
    
    def update_buttons(*args):
        """更新按钮文字"""
        try:
            quantity_str = quantity_var.get()
            quantity = int(quantity_str) if quantity_str and quantity_str.isdigit() else 1
            
            if quantity <= 1:
                start_button.config(text="开始发货")
                direct_button.config(text="直接发货")
                status_label.config(text="单个发货模式")
            else:
                start_button.config(text=f"发货{quantity}个")
                direct_button.config(text=f"直接发货{quantity}个")
                status_label.config(text=f"多数量发货模式 ({quantity}个)")
        except:
            start_button.config(text="开始发货")
            direct_button.config(text="直接发货")
            status_label.config(text="输入有误，使用默认模式")
    
    # 绑定事件
    quantity_var.trace('w', update_buttons)
    
    # 初始化
    update_buttons()
    
    print("✅ 演示GUI已创建")
    print("💡 在数量输入框中输入不同数字，观察按钮文字变化")
    print("🔢 支持的数量范围: 1-99")
    
    return root

def show_feature_summary():
    """显示功能总结"""
    print("\n📋 动态按钮功能总结")
    print("=" * 40)
    print("🎯 核心功能:")
    print("1. 根据数量字段动态更新按钮文字")
    print("2. 数量为1时显示原始文字")
    print("3. 数量>1时显示'发货X个'")
    print("4. 自动验证数量有效性")
    print("5. 多数量时显示确认对话框")
    print()
    print("🔧 技术实现:")
    print("- 使用StringVar.trace()监听数量变化")
    print("- 动态更新按钮config(text=...)")
    print("- 统一的数量验证逻辑")
    print("- 智能的确认对话框触发")
    print()
    print("🎮 用户体验:")
    print("- 直观的按钮文字提示")
    print("- 无需额外的多数量按钮")
    print("- 统一的操作界面")
    print("- 清晰的数量反馈")

def main():
    """主函数"""
    print("🔘 动态按钮功能测试工具")
    print("=" * 50)
    
    # 运行逻辑测试
    test_button_text_logic()
    test_quantity_validation()
    test_confirmation_logic()
    
    # 显示功能总结
    show_feature_summary()
    
    print("\n🎉 所有测试完成！")
    print("\n💡 主要改进:")
    print("- ✅ 删除了独立的多数量发货按钮")
    print("- ✅ 现有按钮根据数量动态显示文字")
    print("- ✅ 统一的发货入口，简化界面")
    print("- ✅ 智能的数量识别和处理")
    
    # 询问是否显示演示GUI
    try:
        choice = input("\n是否显示演示GUI? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            print("\n🖥️ 启动演示GUI...")
            demo_root = create_demo_gui()
            demo_root.mainloop()
    except KeyboardInterrupt:
        print("\n👋 测试结束")

if __name__ == "__main__":
    main()
