import os
import sys
import time
from dm.大漠中文版 import 注册大漠_简
from database import DatabaseManager
from mq_listener import MQListener
import threading
from datetime import datetime
import sqlite3
import json
import mysql.connector
import base64
import pika

from config import MYSQL_CONFIG,RABBITMQ_CONFIG,MQ_ENABLED
import requests
from requests_toolbelt import MultipartEncoder

import ddddocr
import os
import cv2
import numpy as np

import re
from ocr_config import *
from ai_ocr import get_ai_recognizer, ai_match_appearance, ai_find_take_button

# 检查Python版本是否为32位
if sys.maxsize > 2**32:
    raise SystemError('必须使用32位Python版本！当前为64位Python')

class GameScript:
    def __init__(self, stop_event=None):
        # 初始化大漠
        print("初始化大漠插件...")
        self.dm = 注册大漠_简('lumiku2fdc744d96597f65888674a63fb3489a', 'yk38979202')
        if not self.dm:
            raise Exception("大漠插件初始化失败")
        
        print("正在初始化OCR...")
        self.ocr = ddddocr.DdddOcr(beta=True)
        # 设置全局路径
        current_path = os.path.dirname(os.path.abspath(__file__))
        self.dm.SetPath(current_path)
        
        # 加载账号配置
        self.account_config = self.load_account_config()
        
        self.game_hwnd = None
        self.db_manager = None
        self.mq_listener = None

        # 当前任务信息
        self.current_appearance = ""
        self.current_character = ""
        self.current_order_id = ""
        self.current_guild = None
        self.character_name = None
        self.order_id = None
        self.region = None
        self.account = None
        self.last_account = None
        self.last_region = None
        self.last_guild = None
        self.stop_event = stop_event  # 保存停止标志
        self.server_name = None  # 添加服务器名称属性
        self.guild_name = None  # 添加帮会名称属性
        self.image_path1 = None
        self.image_path2 = None
        print("初始化完成")
        self.init_db()

    def init_db(self):
        """初始化数据库"""
        self.conn = sqlite3.connect('game_status.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                last_account TEXT,
                last_region TEXT,
                last_guild TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        self.conn.commit()


    def write_status_to_file(self, message):
        """将当前状态写入到文件"""
        with open("game_status.txt", "a") as f:
            f.write(f"last_account: {message.account}, last_region: {message.region}, last_guild: {message.guild}\n")

    def initialize(self):
        """初始化数据库和MQ连接"""
        try:
            print("初始化数据库连接...")
            self.db_manager = DatabaseManager()

            if not self.db_manager.connect_mysql():
                return False
            if not self.db_manager.connect_redis():
                return False

            # 检查MQ是否启用
            if MQ_ENABLED:
                print("MQ功能已启用，初始化MQ连接...")
                self.mq_listener = MQListener(self)  # 传递GameScript实例
                if not self.mq_listener.connect():
                    return False

                # 添加心跳检测
                def check_connection():
                    while True:
                        try:
                            if not self.mq_listener.is_connected():
                                print("MQ连接已断开，准备重连...")
                                raise Exception("MQ连接断开")
                            time.sleep(5)  # 每5秒检查一次
                        except:
                            break

                # 启动心跳检测线程
                self.heartbeat_thread = threading.Thread(target=check_connection, daemon=True)
                self.heartbeat_thread.start()
            else:
                print("⚠️  MQ功能已禁用，跳过MQ连接初始化")
                self.mq_listener = None

            return True
        except Exception as e:
            print(f"初始化失败: {str(e)}")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            if MQ_ENABLED and self.mq_listener:
                try:
                    # 完全关闭MQ连接
                    self.mq_listener.channel.stop_consuming()
                    self.mq_listener.channel.close()
                    self.mq_listener.connection.close()
                    self.mq_listener = None
                except:
                    pass
            
            if self.db_manager:
                self.db_manager.close_connections()
            
            if self.game_hwnd:
                self.dm.UnBindWindow()
            
        except Exception as e:
            print(f"清理资源失败: {str(e)}")

    def find_game_window(self):
        """查找游戏窗口"""
        print("查找游戏窗口...")
        hwnd = self.dm.FindWindow("KGWin32App", "")
        if hwnd:
            print(f"找到游戏窗口，句柄: {hwnd}")
            self.game_hwnd = hwnd

            # 绑定窗口
            if not self.bind_window():
                print("绑定窗口失败")
                return 0
                
            # 绑定成功后，将窗口置顶
            result = self.dm.SetWindowState(hwnd, 8)  # 8表示置顶
            print(f"窗口已置顶{result}")
            
            return hwnd
        print("未找到游戏窗口")
        return 0

    def bind_window(self):
        """绑定窗口"""
        if not self.game_hwnd:
            return False
        
        print("绑定窗口...")
        result = self.dm.BindWindow(self.game_hwnd, "normal", "windows", "windows", 0)
        if result == 1:
            print(f"窗口绑定成功，使用模式: normal, windows, windows")
            self.dm.SetWindowSize(self.game_hwnd, 1296,759)
            return True
        
        return False

    def login_game(self, account, password):
        """登录游戏"""
        try:
            hwnd = self.dm.FindWindow("KGWin32App", "")
            if hwnd:
                result = self.dm.BindWindow(hwnd, "normal", "normal", "normal", 0)
                if result == 1:
                    print(f"登录窗口绑定成功，使用模式: normal, normal, normal")
                else:
                    print(f"登录窗口绑定失败，使用模式: normal, normal, normal")
            
            print(f"准备登录账号: {account}")
            
            
            # 点击账号输入框
            print("点击账号输入框...")
            self.dm.MoveTo(698, 261)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)
            
            # 清除现有账号
            print("清除现有账号...")
            for _ in range(20):
                self.dm.KeyPress(8)  # Backspace
                time.sleep(0.1)
            
            # 输入账号
            print(f"输入账号: {account}")
            self.dm.KeyPressStr(account,50)
            time.sleep(0.5)
            
            print("点击密码输入框...")
            self.dm.MoveTo(647, 335)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)
            
            # 输入密码
            print(f"输入密码...")
            self.dm.KeyPressStr(password,50)
            time.sleep(0.5)
            
            # 点击登录
            self.dm.MoveTo(639, 411)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            self.dm.Capture(720,387,762,410,"login.png")
            time.sleep(0.5)
            print("正在识别图片...")
            with open("login.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")
                if '同意' in result1:
                    print("点击同意")
                    self.dm.MoveTo(720,387)
                    time.sleep(0.5)
                    self.dm.LeftClick()
            print("登录操作完成")

            self.find_game_window()
            return True
            
        except Exception as e:
            print(f"登录失败: {str(e)}")
            return False

    def find_pic_retry(self, x1, y1, x2, y2, pic_name, delta_color="202020", initial_sim=1, dir=0, retry_times=8, retry_interval=0.3):
        """
        找图重试方法
        
        Args:
            x1, y1, x2, y2: 查找区域坐标
            pic_name: 图片名称
            delta_color: 色偏，默认"202020"
            initial_sim: 初始相似度，默认1
            dir: 查找方向，默认0
            retry_times: 重试次数，默认3次
            retry_interval: 重试间隔，默认0.5秒
        
        Returns:
            tuple: (ret, x, y) 其中ret为-1表示未找到，否则返回找到的图片索引
        """
        sim = initial_sim  # 使用初始相似度
        for i in range(retry_times):
            result = self.dm.FindPic(x1, y1, x2, y2, pic_name, delta_color, sim, dir)
            if result[0] != -1:
                return result
            print(f"找图中。。。当前相似度: {sim}")
            time.sleep(retry_interval)
            sim = max(0.9, sim - 0.01)  # 每次降低相似度，最小为0.8
        return result
        

    def select_game_server(self, region):
        """选择游戏大区"""
        try:
            print(f"准备选择大区: {region}")
            time.sleep(6)
            self.dm.MoveTo(942,77)
            self.dm.LeftClick()
            self.dm.LeftClick()
            time.sleep(1)
            
            print("点击更改服务器按钮...")
            self.dm.MoveTo(785, 403)
            time.sleep(1)
            self.dm.LeftClick()
            time.sleep(1)
            self.dm.LeftClick()
            time.sleep(1)
            
            
            # 如果region为"天鹅坪"或者"破阵子"，则点击双线区，否则点击电信区
            if region in ["天鹅坪", "破阵子"]:
                print("点击双线区...")
                self.dm.MoveTo(91,360)
                time.sleep(0.5)
                self.dm.LeftClick()
                time.sleep(1)
            else:
                # 找图 点击电信区
                print("点击电信区...")
                self.dm.MoveTo(99, 295)
                time.sleep(0.5)
                self.dm.LeftClick()
                time.sleep(1)
            
            # 在服务器列表区域循环识字找到对应的服务器
            for i in range(3):
                for j in range(4):
                    x1 = 259 + 262*j
                    y1 = 90 + 55*i
                    x2 = 347 + 262*j
                    y2 = 114 + 55*i
                    print(f"截图范围：{x1},{y1},{x2},{y2}")
                    self.dm.Capture(x1,y1,x2,y2,"server.png")
                    time.sleep(0.5)
                    print("正在识别图片...")
                    with open("server.png", 'rb') as f:
                        result1 = self.ocr.classification(f.read())
                        print(f"识别结果: {result1}")
                        if self.region in result1:
                            print(f"找到{self.region}")
                            self.dm.MoveTo(x1,y1)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(0.5)

                            self.dm.MoveTo(1106,668)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            
                            print("大区选择完成")
                            time.sleep(2)
                            
                            # 点击进入游戏
                            print("点击进入游戏...")
                            self.dm.MoveTo(635, 496)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(5)
                            self.dm.MoveTo(1116,664)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            
                            # 等待15秒加载游戏
                            print("等待游戏加载...")
                            for i in range(15):
                                print(f"加载中 {i+1}/15")
                                self.dm.MoveTo(859,654)
                                self.dm.LeftClick()
                                time.sleep(1)
                            
                            return True
            print(f"选择大区失败: 找不到")
            return False
        except Exception as e:
            print(f"选择大区失败: {str(e)}")
            return False

    def check_stop(self):
        """检查是否需要停止"""
        if self.stop_event and self.stop_event.is_set():
            print("收到停止信号")
            return True
        return False

    def execute_delivery_task(self):
        """执行发货任务"""
        try:
            print("开始发货任务...")

            # 在关键操作点检查停止信号
            if self.check_stop():
                return False

            # 点击背包按钮
            print("点击背包按钮成功")
            self.dm.MoveTo(1051, 39)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 点击仓库按钮
            print("点击仓库按钮成功")
            self.dm.MoveTo(836, 671)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 点击帮会按钮
            print("点击帮会按钮成功")
            self.dm.MoveTo(39, 281)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 判断是否进入解锁流程
            self.dm.Capture(588,540,691,567,"lock.png")
            time.sleep(0.5)
            print("正在识别图片...")
            with open("lock.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")

                # 如果找到锁住图片，则进入解锁流程
                if '一键解锁' in result1:
                    print("进入解锁流程...")
                    # 点击解锁按钮
                    self.dm.MoveTo(588,540)
                    time.sleep(0.5)
                    self.dm.LeftClick()

                    # 等待解锁完成：每5秒检测锁住的图片是否消失
                    for i in range(30):
                        print(f"等待解锁完成 {i+1}/30")
                        time.sleep(5)
                        # 查找是否已经解锁成功
                        self.dm.Capture(588,540,691,567,"lock.png")
                        time.sleep(0.5)
                        print("正在识别图片...")
                        with open("lock.png", 'rb') as f:
                            result1 = self.ocr.classification(f.read())
                            print(f"识别结果: {result1}")
                        if '解锁' not in result1:
                            #解锁成功，点击esc，再点击一次帮会
                            self.dm.KeyPress(27)
                            time.sleep(0.5)

                            self.dm.MoveTo(836, 671)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(1)
                            self.dm.MoveTo(39, 281)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(1)
                            break


                        if i == 29:
                            raise Exception("解锁失败")
                else:
                    print("未进入解锁流程")


            # 执行后续流程
            self.execute_after_guild1()
            print("发货任务完成")
            return True

        except Exception as e:
            raise Exception(f"发货任务失败: {str(e)}")

    def execute_direct_delivery(self, appearance_name, character_name, order_id=""):
        """直接执行发货任务，跳过登录过程"""
        try:
            print(f"🚀 开始直接发货任务...")
            print(f"📦 外观: {appearance_name}")
            print(f"👤 角色: {character_name}")
            print(f"🆔 订单ID: {order_id}")

            # 检查游戏窗口是否存在
            if not self.game_hwnd:
                hwnd = self.find_game_window()
                if not hwnd:
                    raise Exception("未找到游戏窗口，请确保游戏已启动并登录")
                if not self.bind_window():
                    raise Exception("绑定游戏窗口失败")

            # 设置当前任务信息
            self.current_appearance = appearance_name
            self.character_name = character_name
            self.order_id = order_id

            # 设置默认值（直接发货时不需要这些信息）
            self.region = "直接发货"
            self.current_guild = "直接发货"
            self.account = "直接发货"

            # 在关键操作点检查停止信号
            if self.check_stop():
                return False

            # 点击背包按钮
            print("点击背包按钮...")
            self.dm.MoveTo(1051, 39)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 点击仓库按钮
            print("点击仓库按钮...")
            self.dm.MoveTo(836, 671)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 点击帮会按钮
            print("点击帮会按钮...")
            self.dm.MoveTo(39, 281)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            # 判断是否进入解锁流程
            self.dm.Capture(588,540,691,567,"lock.png")
            time.sleep(0.5)
            print("正在识别解锁状态...")
            with open("lock.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")

                # 如果找到锁住图片，则进入解锁流程
                if '一键解锁' in result1:
                    print("🔓 进入解锁流程...")
                    # 点击解锁按钮
                    self.dm.MoveTo(588,540)
                    time.sleep(0.5)
                    self.dm.LeftClick()

                    # 等待解锁完成：每5秒检测锁住的图片是否消失
                    for i in range(30):
                        print(f"⏳ 等待解锁完成 {i+1}/30")
                        time.sleep(5)
                        # 查找是否已经解锁成功
                        self.dm.Capture(588,540,691,567,"lock.png")
                        time.sleep(0.5)
                        print("正在识别解锁状态...")
                        with open("lock.png", 'rb') as f:
                            result1 = self.ocr.classification(f.read())
                            print(f"识别结果: {result1}")
                        if '解锁' not in result1:
                            print("✅ 解锁成功，重新进入帮会仓库...")
                            #解锁成功，点击esc，再点击一次帮会
                            self.dm.KeyPress(27)
                            time.sleep(0.5)

                            self.dm.MoveTo(836, 671)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(1)
                            self.dm.MoveTo(39, 281)
                            time.sleep(0.5)
                            self.dm.LeftClick()
                            time.sleep(1)
                            break

                        if i == 29:
                            raise Exception("解锁失败，请检查帮会仓库状态")
                else:
                    print("✅ 帮会仓库已解锁")

            # 执行发货流程
            print("🎯 开始执行发货流程...")
            self.execute_after_guild1()
            print("✅ 直接发货任务完成")
            return True

        except Exception as e:
            raise Exception(f"直接发货任务失败: {str(e)}")

    def handle_game_message(self, message):
        """处理游戏消息"""
        try:

            # 从文件中读取当前状态
            self.read_status_from_file()

            # 打印当前状态
            print(f"当前状态: last_account={self.last_account}, last_region={self.last_region}, last_guild={self.last_guild}")
            print(f"收到游戏任务消息:")
            print(f"账号: {message.account}")
            print(f"大区: {message.region}")
            print(f"帮会: {message.guild}")
            print(f"外观: {message.appearance}")
            print(f"角色名称: {message.character_name}")
            print(f"订单ID: {message.order_id}")

            self.current_guild = message.guild
            self.current_appearance = message.appearance
            self.character_name = message.character_name
            self.region = message.region
            self.account = message.account
            self.order_id = message.order_id

            # 检查是否需要重新登录
            need_relogin = (
                self.last_account is not None and  # 不是第一次运行
                (message.account != self.last_account or  # 账号不同
                 message.region != self.last_region or    # 大区不同
                 message.guild != self.last_guild)        # 帮会不同
            )
            
            # 检查游戏是否已启动
            self.game_hwnd = self.find_game_window()

            #激活窗口
            self.dm.SetWindowState(self.game_hwnd, 1)
            
            if self.check_stop():
                return
            
            if not self.game_hwnd:
                print("游戏未启动，请先启动游戏...")
                raise Exception("游戏未启动，请先启动游戏...")
            
            # 登录游戏
            if need_relogin:
                print("检测当前页面是否是游戏中！")
                result = self.find_pic_retry(
                    0,0,2000,2000,
                    "images/待登录按钮.bmp"
                )
                if result[0] == -1:
                    print("需要重新登录...")
                    if not self.logout_game():
                        print("退出游戏失败，终止操作")
                        raise Exception("退出游戏失败，终止操作")
                # 重新登录
                if not self.login_game(message.account, message.password):
                    print("重新登录失败，终止操作")
                    raise Exception("重新登录失败，终止操作")
                    # 重新选择游戏服务器
                if not self.select_game_server(message.region):
                    print("重新选择游戏服务器失败，终止操作")
                    raise Exception("重新选择游戏服务器失败，终止操作")
            else:
                print("不需要退出登录")
                # 检测当前页面是否是登录页面
                time.sleep(5)
                result = self.find_pic_retry(
                    0,0,2000,2000,
                    "images/待登录按钮.bmp",
                    "202020"
                )
                if result[0] != -1:
                    print("当前页面是登录页面")
                    # 执行登录流程
                    if not self.login_game(message.account, message.password):
                        print("重新登录失败，终止操作")
                        raise Exception("重新登录失败，终止操作")
                    # 重新选择游戏服务器
                    if not self.select_game_server(message.region):
                        print("重新选择游戏服务器失败，终止操作")
                        raise Exception("重新选择游戏服务器失败，终止操作")
                else:
                    print("当前页面不是登录页面")
            
            # 写入当前状态到文件
            self.write_status_to_file(message)

            for i in range(5):
                # 点击esc
                print("点击esc...")
                self.dm.KeyPress(27)
                time.sleep(0.5)

                self.dm.Capture(1222,688,1256,705,"logout.png")
                time.sleep(0.5)
                print("正在识别图片...")
                with open("logout.png", 'rb') as f:
                    result1 = self.ocr.classification(f.read())
                    print(f"识别结果: {result1}")
                    if '退出' in result1:
                        # 点击esc
                        print("点击esc...")
                        self.dm.KeyPress(27)
                        time.sleep(0.5)
                        break
            # 执行完整的发货任务
            if not self.execute_delivery_task():
                print("发货任务失败，终止操作")
                raise Exception("发货任务失败，终止操作")
                    
            #更新订单
            self.update_order_status(
                order_id=self.order_id,
                character_name=self.character_name,
                image_path=[self.image_path1,self.image_path2],
                is_success=True

            )


        except Exception as e:
            print(f"处理消息失败: {str(e)}")
            # 更新失败状态
            self.update_order_status(
                order_id=self.order_id,
                is_success=False,
                error_message=str(e)
            )
            return

    def read_status_from_file(self):
        """从文件中读取当前状态"""
        if os.path.exists("game_status.txt"):
            with open("game_status.txt", "r") as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    parts = last_line.split(", ")
                    for part in parts:
                        key, value = part.split(": ")
                        if key == "last_account":
                            self.last_account = value
                        elif key == "last_region":
                            self.last_region = value
                        elif key == "last_guild":
                            self.last_guild = value

    def logout_game(self):
        """退出游戏到登录界面"""
        try:
            time.sleep(3)
            print("开始退出游戏流程...")
            self.dm.SetWindowState(self.game_hwnd, 1)
            
            for i in range(5):
                # 点击esc
                print("点击esc...")
                self.dm.KeyPress(27)
                time.sleep(0.5)

                self.dm.Capture(1222,688,1256,705,"logout.png")
                time.sleep(0.5)
                print("正在识别图片...")
                with open("logout.png", 'rb') as f:
                    result1 = self.ocr.classification(f.read())
                    print(f"识别结果: {result1}")
                    if '退出' in result1:
                        break
            print("点击退出")
            self.dm.MoveTo(1240,675)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            print("点击返回登录")
            self.dm.MoveTo(636,538)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(3)

            for i in range(2):
                self.dm.MoveTo(938,29)
                time.sleep(0.5)
                self.dm.LeftClick()
                time.sleep(1)

            print("点击切换账号")
            self.dm.MoveTo(1240,115)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            self.dm.BindWindow(self.game_hwnd, "normal", "normal", "normal", 0)
            print("点击其他方式登录")
            self.dm.MoveTo(765,500)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            return True
            
        except Exception as e:
            print(f"退出游戏失败: {str(e)}")
            return False

    def script_main(self):
        """脚本的主入口方法"""
        try:
            print("开始执行游戏自动化脚本...")
            
            while True:  # 添加无限循环
                try:
                    # 确保完全清理旧连接
                    try:
                        self.cleanup()
                    except:
                        pass
                    
                    time.sleep(1)  # 等待旧连接完全关闭
                    
                    # 初始化连接
                    if not self.initialize():
                        print("初始化连接失败，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                    # 初始化时查找游戏窗口
                    hwnd = self.find_game_window()
                    if hwnd:
                        print(f"游戏已经运行，窗口句柄: {hwnd}")
                        if not self.bind_window():
                            print("初始绑定窗口失败，5秒后重试...")
                            time.sleep(5)
                            continue
                    
                    # 启动消息监听（仅在MQ启用时）
                    if MQ_ENABLED and self.mq_listener:
                        def message_handler(message):
                            self.handle_game_message(message)

                        print("开始监听游戏任务消息...")
                        self.mq_listener.start_listening(message_handler)
                    else:
                        print("🔄 MQ功能已禁用，进入待机模式...")
                        print("💡 要启用MQ功能，请在config.py中设置 MQ_ENABLED = True")
                        # 进入无限等待，避免程序退出
                        while True:
                            time.sleep(60)  # 每分钟检查一次
                            print("⏰ 待机中... (MQ功能已禁用)")
                    
                except Exception as e:
                    print(f"连接异常，5秒后重试: {str(e)}")
                    time.sleep(5)
                
        except KeyboardInterrupt:
            print("脚本被用户中断")
        finally:
            self.cleanup()


    def is_one_char_diff(self, s1, s2):
        """判断两个字符串是否只有一个字符不同"""
        # 如果两个字符串完全相同，直接返回True
        if s1 == '风明雪华披' and s2 == '风风明雪华披':
            return True
        if s1 == '挂宠小黑猫' and s2 == '挂宠小小黑猫':
            return True
        if s1 == '金发俏小仙' and s2 == '金发俏小小仙':
            return True
        if s1 == '一点沧洲潋滟标准' and s2 == '一点沧洲激艳标准':
            return True
        if s1 == s2:
            return True
        # 长度不同则直接排除
        if len(s1) != len(s2):
            return False
        # 统计不同字符的数量
        diff = 0
        for c1, c2 in zip(s1, s2):
            if c1 != c2:
                diff += 1
                # 提前终止：差异超过1次无需继续
                if diff > 1:
                    return False
        return diff == 1
 
        

    def keep_chinese(self,text):
        # 匹配所有中文字符（包括基本汉字和扩展区）
        pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002ebef]')
        return ''.join(pattern.findall(text))

    def ai_match_appearance_name(self, image_path, target_name):
        """
        使用AI判断外观名称是否匹配
        """
        try:
            print(f"🤖 使用通义千问AI判断外观匹配: {target_name}")

            # 使用AI判断匹配
            is_match = ai_match_appearance(image_path, target_name)

            if is_match:
                print(f"✅ AI判断匹配成功: {target_name}")
                return True
            else:
                print(f"❌ AI判断不匹配: {target_name}")
                return False

        except Exception as e:
            print(f"❌ AI判断出错: {e}，回退到OCR识别")
            # 出错时回退到传统OCR+模糊匹配
            appearance_result = self.enhanced_ocr_recognition(image_path)
            appearance_result_clean = self.keep_chinese(appearance_result.replace("·","").replace("（","").replace("）",""))
            return self.fuzzy_match_appearance(target_name, appearance_result_clean)

    def ai_find_take_button_in_screen(self):
        """
        使用AI在整个游戏屏幕中查找取出按钮
        """
        try:
            print(f"🤖 使用通义千问AI查找取出按钮")

            # 截取整个游戏屏幕
            screen_image = "game_screen.png"
            self.dm.Capture(0, 0, 2000, 2000, screen_image)
            time.sleep(0.5)

            # 使用AI查找按钮
            button_info = ai_find_take_button(screen_image)

            if button_info["found"]:
                print(f"✅ AI找到取出按钮: 类型={button_info['type']}, 坐标=({button_info['x']}, {button_info['y']})")
                return button_info
            else:
                print(f"❌ AI未找到取出按钮")
                return {"found": False, "type": None, "x": -1, "y": -1}

        except Exception as e:
            print(f"❌ AI查找按钮出错: {e}")
            return {"found": False, "type": None, "x": -1, "y": -1}

    def enhanced_ocr_recognition(self, image_path, retry_count=None):
        """增强的OCR识别，支持多次重试和预处理"""
        if retry_count is None:
            retry_count = OCR_RETRY_COUNT

        best_result = ""
        best_confidence = 0

        # 创建调试目录
        if DEBUG_MODE and not os.path.exists(DEBUG_IMAGE_PATH):
            os.makedirs(DEBUG_IMAGE_PATH)

        for attempt in range(retry_count):
            try:
                # 读取图片
                img = cv2.imread(image_path)
                if img is None:
                    print(f"无法读取图片: {image_path}")
                    continue

                # 图像预处理提高识别率
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

                # 尝试不同的预处理方法
                processed_images = []
                processing_names = []

                # 原图
                processed_images.append(gray)
                processing_names.append("original")

                # 二值化
                _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                processed_images.append(binary)
                processing_names.append("binary")

                # 高斯模糊后二值化
                kernel_size = IMAGE_PROCESSING['gaussian_blur_kernel']
                blurred = cv2.GaussianBlur(gray, kernel_size, 0)
                _, binary_blur = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                processed_images.append(binary_blur)
                processing_names.append("blur_binary")

                # 形态学操作
                morph_kernel_size = IMAGE_PROCESSING['morphology_kernel_size']
                kernel = np.ones(morph_kernel_size, np.uint8)
                morph = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
                processed_images.append(morph)
                processing_names.append("morphology")

                # 对每种预处理结果进行OCR识别
                for idx, (processed_img, process_name) in enumerate(zip(processed_images, processing_names)):
                    # 保存预处理后的图片
                    temp_path = f"temp_processed_{process_name}_{attempt}.png"
                    cv2.imwrite(temp_path, processed_img)

                    # 调试模式下保存图片
                    if DEBUG_MODE:
                        debug_path = os.path.join(DEBUG_IMAGE_PATH, f"debug_{process_name}_{attempt}_{os.path.basename(image_path)}")
                        cv2.imwrite(debug_path, processed_img)

                    # OCR识别
                    with open(temp_path, 'rb') as f:
                        result = self.ocr.classification(f.read())

                    # 应用字符纠错
                    corrected_result = self.apply_ocr_corrections(result)

                    print(f"OCR识别[{process_name}]: 原始='{result}' 纠错后='{corrected_result}'")

                    # 清理临时文件
                    try:
                        os.remove(temp_path)
                    except:
                        pass

                    # 计算置信度（基于中文字符数量和长度）
                    chinese_chars = self.keep_chinese(corrected_result)
                    confidence = len(chinese_chars) * 10 + (10 if len(corrected_result) > 2 else 0)

                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_result = corrected_result
                        print(f"更新最佳结果: '{best_result}' (置信度: {best_confidence})")

                time.sleep(0.1)  # 短暂延迟

            except Exception as e:
                print(f"OCR识别尝试 {attempt + 1} 失败: {str(e)}")
                continue

        print(f"最终OCR识别结果: '{best_result}' (置信度: {best_confidence})")
        return best_result if best_result else ""

    def apply_ocr_corrections(self, text):
        """应用OCR识别错误纠正"""
        corrected = text
        for wrong, correct in OCR_ERROR_CORRECTIONS.items():
            corrected = corrected.replace(wrong, correct)
        return corrected

    def clean_appearance_name(self, name):
        """清理外观名称，移除特殊字符"""
        cleaned = name
        for char, replacement in CHAR_REPLACEMENTS.items():
            cleaned = cleaned.replace(char, replacement)
        return self.keep_chinese(cleaned)

    def fuzzy_match_appearance(self, target_name, recognized_name, threshold=None):
        """模糊匹配外观名称，提高匹配成功率"""
        if threshold is None:
            threshold = FUZZY_MATCH_THRESHOLD

        # 清理字符串
        target_clean = self.clean_appearance_name(target_name)
        recognized_clean = self.clean_appearance_name(recognized_name)

        print(f"🔍 模糊匹配分析:")
        print(f"   目标名称: '{target_name}' -> '{target_clean}'")
        print(f"   识别结果: '{recognized_name}' -> '{recognized_clean}'")

        # 空字符串检查
        if not target_clean or not recognized_clean:
            print("❌ 存在空字符串，匹配失败")
            return False

        # 1. 完全匹配
        if target_clean == recognized_clean:
            print("✅ 完全匹配成功")
            return True

        # 2. 使用现有的单字符差异匹配
        if self.is_one_char_diff(target_clean, recognized_clean):
            print("✅ 单字符差异匹配成功")
            return True

        # 3. 关键词匹配（如果启用）
        if SPECIAL_APPEARANCE_RULES['keyword_match']:
            # 检查是否包含足够长的公共子串
            min_length = SPECIAL_APPEARANCE_RULES['min_match_length']
            for i in range(len(target_clean) - min_length + 1):
                substring = target_clean[i:i + min_length]
                if substring in recognized_clean:
                    print(f"✅ 关键词匹配成功: '{substring}'")
                    return True

        # 4. 部分匹配（包含匹配）
        if SPECIAL_APPEARANCE_RULES['partial_match']:
            min_len = max(SPECIAL_APPEARANCE_RULES['min_match_length'], 3)
            if len(target_clean) >= min_len and len(recognized_clean) >= min_len:
                if target_clean in recognized_clean or recognized_clean in target_clean:
                    print("✅ 部分匹配成功")
                    return True

        # 5. 相似度匹配（基于公共字符数）
        if len(target_clean) > 0 and len(recognized_clean) > 0:
            common_chars = set(target_clean) & set(recognized_clean)
            similarity = len(common_chars) / max(len(set(target_clean)), len(set(recognized_clean)))
            print(f"   相似度: {similarity:.2f} (阈值: {threshold})")

            if similarity >= threshold:
                print(f"✅ 相似度匹配成功")
                return True

        # 6. 编辑距离匹配（Levenshtein距离）
        edit_distance = self.calculate_edit_distance(target_clean, recognized_clean)
        max_distance = max(len(target_clean), len(recognized_clean)) // 3  # 允许1/3的字符不同
        print(f"   编辑距离: {edit_distance} (最大允许: {max_distance})")

        if edit_distance <= max_distance and edit_distance > 0:
            print("✅ 编辑距离匹配成功")
            return True

        print("❌ 所有匹配方法均失败")
        return False

    def calculate_edit_distance(self, s1, s2):
        """计算两个字符串的编辑距离（Levenshtein距离）"""
        if len(s1) < len(s2):
            return self.calculate_edit_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    def execute_after_guild1(self):
        """点击帮1之后的操作流程"""
        try:
            #点击输入框
            self.dm.MoveTo(497, 36)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)
            self.dm.MoveTo(378, 36)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)
            # 在搜索框中输入外观全称
            print(f"输入外观全称: {self.current_appearance}")
            self.dm.SendString(self.game_hwnd,self.current_appearance)
            time.sleep(1)

            for i in range(6):
                print(f"点击第{i + 1}页")
                self.dm.MoveTo(134, 90 + (i) *64 )
                time.sleep(0.5)
                self.dm.LeftClick()
                time.sleep(0.5)

                # 添加一个标志变量
                should_break = False
                appearance_name =self.current_appearance.replace("·","")
                appearance_name = appearance_name.replace("（","")
                appearance_name = appearance_name.replace("）","")
                print(f"待查找外观：{appearance_name}")

                for i1 in range(5):
                    for i2 in range(5):

                        print(f"点击第{i1 + 1}行，第{i2 + 1}格:{212 + (i2) * 72},{105 + (i1) * 72}")
                        self.dm.MoveTo(204 + (i2) * 72, 101 + (i1) * 72)
                        time.sleep(0.5)
                        self.dm.LeftClick()
                        time.sleep(0.5)

                        flag = True
                        print(f"使用AI查找取出按钮")

                        # 使用AI查找取出按钮
                        button_info = self.ai_find_take_button_in_screen()

                        if button_info["found"]:
                            print(f"✅ AI找到取出按钮: 类型={button_info['type']}, 坐标=({button_info['x']}, {button_info['y']})")
                            flag = False

                            # 先识别外观名称确认是否匹配
                            self.dm.Capture(394,36,780,283,"appearance_name.png")
                            time.sleep(0.5)
                            print("正在识别外观名称...")

                            # 使用AI判断外观匹配
                            if self.ai_match_appearance_name("appearance_name.png", appearance_name):
                                print(f"✓ 成功匹配外观: {appearance_name}")
                                print(f"执行取出操作...")

                                # 点击AI找到的取出按钮
                                self.dm.MoveTo(button_info['x'], button_info['y'])
                                time.sleep(0.5)
                                self.dm.LeftClick()
                                time.sleep(0.5)

                                # 根据按钮类型执行不同操作
                                if button_info['type'] == 'single':
                                    print("单个取出，直接关闭窗口")
                                elif button_info['type'] == 'multiple':
                                    print("多个取出，需要点击全部取出")
                                    # 可能需要额外的点击操作
                                    for _ in range(20):
                                        self.dm.MoveTo(556,555)
                                        self.dm.LeftClick()
                                        time.sleep(0.2)

                                # 关闭窗口
                                self.dm.MoveTo(1241,34)
                                time.sleep(0.5)
                                self.dm.LeftClick()
                                time.sleep(0.5)

                                self.execute_friend_and_mail()
                                return True




                        if flag:
                            print("当前页无物品，去往下一页")
                            should_break = True
                            break  # 退出i2循环                                                              
                            
                    if should_break:
                        break  # 退出i1循环

            raise Exception("未找到物品！")
        except Exception as e:
            raise Exception(f"帮1后续操作失败: {str(e)}")

    def execute_friend_and_mail(self):
        """执行发信操作"""
        try:
            
            
            print(f"点击菜单")
            self.dm.MoveTo(1254,33)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            print(f"点击邮件")
            self.dm.MoveTo(1242,200)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            print("点击寄信")
            self.dm.MoveTo(93,654)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)
            
            print("点击寄信查找标签...")
            self.dm.MoveTo(218, 154)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            # 输入好友昵称
            print(f"输入好友昵称: {self.character_name}")
            self.dm.SendString(self.game_hwnd,self.character_name)
            time.sleep(0.5)

            

            print("点击信件主题...")
            self.dm.MoveTo(782, 190)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            # 输入信件主题
            print(f"输入信件主题: {self.current_appearance}")
            self.dm.SendString(self.game_hwnd,self.current_appearance)
            time.sleep(0.5)
            
            print("点击添加物品按钮")
            self.dm.MoveTo(538, 531)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            print("点击背包搜索框")
            self.dm.MoveTo(377, 24)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            self.dm.MoveTo(233, 28)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)
            self.dm.SendString(self.game_hwnd,self.current_appearance)

            print("点击搜索到的外观")
            self.dm.MoveTo(125, 113)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            self.dm.MoveTo(534,596)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            print("查看物品是否被正确添加到邮件...")
            self.dm.Capture(521,513,554,545,'goods.png')
            print("正在识别图片...")
            with open("goods.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")
                if '+' in result1:
                    print("物品添加到邮件异常！")
                    raise Exception("物品添加到邮件异常！")
                
            # 截图并保存到png目录下，命名规则为：日期时间-外观全称-区服-帮会-角色名.png
            print("截图并保存到png目录下...")
            self.image_path1 = f"png/{datetime.now().strftime('%Y%m%d_%H%M%S')}-{self.current_appearance}-{self.region}-{self.current_guild}-{self.character_name}.png"
            self.dm.CaptureJpg(0,0,2000,2000, self.image_path1,20) 
            time.sleep(1)

            print("点击发送按钮")
            self.dm.MoveTo(830,655)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(0.5)

            print("点击确定发送")
            self.dm.MoveTo(746,540)
            time.sleep(0.5)
            self.dm.LeftClick()
            time.sleep(1)

            print("截图并保存到png目录下...")
            self.image_path2 = f"png/{datetime.now().strftime('%Y%m%d_%H%M%S')}-{self.current_appearance}-{self.region}-{self.current_guild}-{self.character_name}.png"
            self.dm.CaptureJpg(0,0,2000,2000, self.image_path2,20)
            time.sleep(1)

            print("点击ESC两次")
            self.dm.KeyPress(27)
            time.sleep(0.5)
            self.dm.KeyPress(27)
            time.sleep(0.5)

            

            return True
        except Exception as e:
            print(f"发信操作失败: {str(e)}")
            return False

    def load_account_config(self):
        """加载账号配置"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'account_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
        return {f"帮{i}": {"account": "", "password": ""} for i in range(1, 10)}

    def update_order_status(self, order_id, character_name=None, image_path=[], is_success=True, error_message=None):
        """更新订单状态"""
        try:
            # 连接MySQL数据库
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()

            if is_success:
                # 读取图片并转换为base64
                image_base64 = ''
                for path in image_path:
                    if path and os.path.exists(path):
                        with open(path, 'rb') as img_file:
                            file_content = img_file.read()
                        # 构造Multipart数据，指定boundary
                        encoder = MultipartEncoder(
                            fields={
                                'file': (path[4:], file_content, 'image/png')
                            },
                        )
                        # 发送POST请求
                        response = requests.post(
                            'http://wblxt.bingtangzhanghao.cn:8084/common/upload',
                            data=encoder,
                            headers={'Content-Type': encoder.content_type}  # 关键修复点：设置正确的Content-Type
                        )
                        print(f"上传相应内容：{response.text}")
                        # 获取上传返回的路径
                        response_data = response.json()
                        if response_data.get('code') == 200:
                            # 直接获取fileName字段
                            uploaded_path = response_data.get('fileName')
                            
                            if uploaded_path:
                                if image_base64:  # 非首次，添加逗号
                                    image_base64 = image_base64 + "," + uploaded_path
                                else:  # 首次，直接赋值
                                    image_base64 = uploaded_path
                                print(f"✅ 文件上传成功！服务器存储路径: {uploaded_path}")
                                print(f"   原始文件名: {response_data.get('originalFilename')}")
                                print(f"   访问URL: {response_data.get('url')}")
                            else:
                                print("⚠️ 警告：服务器返回空路径")
                        else:
                            print(f"❌ 上传失败：{response_data.get('msg', '未知业务错误')}")

                # 更新订单信息
                sql = """UPDATE game_order 
                        SET character_name = %s, image = %s 
                        WHERE id = %s"""
                cursor.execute(sql, (character_name, image_base64, order_id))
            else:
                # 更新失败状态
                sql = """UPDATE game_order 
                        SET  field4 = %s 
                        WHERE id = %s"""
                cursor.execute(sql, (error_message, order_id))
                print(f"更新订单 {order_id} 失败状态: {error_message}")

            
            conn.commit()
        except Exception as e:
            print(f"更新订单状态失败: {str(e)}")
            
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def send_to_java(self, game_order):
        """发送消息到Java端的更新订单队列"""
        try:
            # 建立RabbitMQ连接
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials
            )
            connection = pika.BlockingConnection(parameters)
            channel = connection.channel()

            # 声明交换机
            channel.exchange_declare(
                exchange='OrderExchange',
                exchange_type='direct',
                durable=True

            )

            # 声明队列
            channel.queue_declare(queue='OrderQueue', durable=True)

            # 绑定队列到交换机
            channel.queue_bind(
                exchange='OrderExchange',
                queue='OrderQueue',
                routing_key='OrderRouting'
            )

            # 发送消息
            channel.basic_publish(
                exchange='OrderExchange',
                routing_key='OrderRouting',
                body=json.dumps(game_order),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 使消息持久化
                    content_type='application/json'
                )
            )

            print(f"已发送更新订单消息到Java端: {game_order}")
            connection.close()
        except Exception as e:
            print(f"发送消息到Java端失败: {str(e)}")
            raise

if __name__ == "__main__":

    # 启动脚本
    script = GameScript()
    script.script_main()
    
            
            