import json
import pika
from config import RABBITMQ_CONFIG
import struct
import traceback

class GameMessage:
    def __init__(self, account, password, region, guild, appearance, character_name, order_id):
        self.account = account
        self.password = password
        self.region = region
        self.guild = guild
        self.appearance = appearance
        self.character_name = character_name
        self.order_id = order_id

class MQListener:
    def __init__(self, game_script):
        self.connection = None
        self.channel = None
        self.game_script = game_script  # 保存GameScript实例

    def connect(self):
        try:
            print("正在连接RabbitMQ服务器...")
            print(f"连接信息: {RABBITMQ_CONFIG}")
            
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                heartbeat=60  # 添加60秒的心跳超时
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            print("声明交换机和队列，并进行绑定...")
            self.channel.exchange_declare(exchange=RABBITMQ_CONFIG['exchange'], exchange_type='direct',durable=True)
            self.channel.queue_declare(queue=RABBITMQ_CONFIG['queue_name'], durable=True)
            self.channel.queue_bind(
                exchange=RABBITMQ_CONFIG['exchange'],
                queue=RABBITMQ_CONFIG['queue_name'],
                routing_key=RABBITMQ_CONFIG['routing_key']
            )

            # 设置每个消费者一次只处理一条消息
            self.channel.basic_qos(prefetch_count=1)
            
            print("RabbitMQ连接成功")
            return True
        except Exception as e:
            print(f"连接过程中发生错误: {str(e)}")
            return False

    def start_listening(self, callback):
        if not self.channel:  # 检查通道是否存在
            print("未成功连接RabbitMQ，无法开始监听")
            return

        def message_handler(ch, method, properties, body):
            try:
                # 直接解析 JSON 消息
                message = json.loads(body.decode('utf-8'))
                print(f"收到消息: {message}")
                
                # 从配置中获取对应帮会的账号密码
                guild = message.get('guild')
                account_info = self.game_script.account_config.get(guild, {})
                
                # 创建 GameMessage 对象
                game_message = GameMessage(
                    account=account_info.get('account'),
                    password=account_info.get('password'),
                    region=message.get('region'),
                    guild=message.get('guild'),
                    appearance=message.get('appearance', ''),
                    character_name=message.get('character_name'),
                    order_id=message.get('order_id')
                )
                
                # 使用GameScript实例设置server_name和guild_name
                self.game_script.server_name = game_message.region
                self.game_script.guild_name = game_message.guild

                # 保存channel和delivery_tag到GameScript实例
                self.game_script.mq_channel = ch
                self.game_script.mq_delivery_tag = method.delivery_tag
                
                callback(game_message)

                # 消息处理成功，确认消息
                # 移除立即确认的代码
                # ch.basic_ack(delivery_tag=method.delivery_tag)
                print("等待业务流程完成后再确认消息")

                
            except Exception as e:
                print(f"消息处理失败: {str(e)}")
                print(traceback.format_exc())
                # 消息处理失败时，将消息重新放回队列
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        print(f"开始监听队列: {RABBITMQ_CONFIG['queue_name']}")
        self.channel.basic_consume(
            queue=RABBITMQ_CONFIG['queue_name'],
            on_message_callback=message_handler,
            auto_ack=False
        )
        self.channel.start_consuming()

    def close(self):
        if self.connection:
            self.connection.close()

    def is_connected(self):
        """检查MQ连接状态"""
        try:
            if not self.connection or not self.connection.is_open:
                return False
            if not self.channel or not self.channel.is_open:
                return False
            # 尝试进行一个无害的操作来验证连接
            self.channel.basic_qos(prefetch_count=1)
            return True
        except Exception as e:
            print(f"检查连接状态时发生错误: {str(e)}")
            return False