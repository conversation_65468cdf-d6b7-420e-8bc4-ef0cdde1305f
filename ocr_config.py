# OCR识别配置文件

# OCR识别重试次数
OCR_RETRY_COUNT = 3

# 模糊匹配相似度阈值
FUZZY_MATCH_THRESHOLD = 0.6

# 图像预处理参数
IMAGE_PROCESSING = {
    'gaussian_blur_kernel': (3, 3),
    'morphology_kernel_size': (2, 2),
    'binary_threshold_type': 'OTSU'  # 可选: OTSU, ADAPTIVE
}

# OCR识别超时时间（秒）
OCR_TIMEOUT = 5

# 是否启用调试模式（保存预处理图片）
DEBUG_MODE = False

# 调试图片保存路径
DEBUG_IMAGE_PATH = "debug_images"

# 特殊字符替换映射
CHAR_REPLACEMENTS = {
    "·": "",
    "（": "",
    "）": "",
    "【": "",
    "】": "",
    " ": "",
    "　": "",  # 全角空格
}

# 常见OCR识别错误映射
OCR_ERROR_CORRECTIONS = {
    "0": "O",
    "1": "I",
    "5": "S",
    "8": "B",
    "6": "G",
}

# 外观名称特殊匹配规则
SPECIAL_APPEARANCE_RULES = {
    # 如果识别结果包含这些关键词，则认为匹配成功
    'keyword_match': True,
    # 最小匹配长度
    'min_match_length': 3,
    # 是否启用部分匹配
    'partial_match': True,
}
