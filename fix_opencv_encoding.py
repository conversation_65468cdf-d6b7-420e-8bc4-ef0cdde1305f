#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复OpenCV文件读取编码问题
"""

import os
import cv2
import numpy as np

def test_opencv_with_chinese_path():
    """测试OpenCV处理中文路径的能力"""
    print("🧪 测试OpenCV中文路径处理...")
    
    # 创建测试图片
    test_img = np.zeros((100, 100, 3), dtype=np.uint8)
    test_img[:] = (255, 255, 255)  # 白色背景
    
    # 测试英文文件名
    english_file = "test_english.png"
    try:
        cv2.imwrite(english_file, test_img)
        loaded = cv2.imread(english_file)
        if loaded is not None:
            print("✅ 英文文件名处理正常")
            os.remove(english_file)
        else:
            print("❌ 英文文件名读取失败")
    except Exception as e:
        print(f"❌ 英文文件名测试失败: {e}")
    
    # 测试中文文件名
    chinese_file = "测试中文.png"
    try:
        # 使用cv2.imencode来避免中文路径问题
        success, encoded_img = cv2.imencode('.png', test_img)
        if success:
            with open(chinese_file, 'wb') as f:
                f.write(encoded_img.tobytes())
            
            # 使用cv2.imdecode来读取
            with open(chinese_file, 'rb') as f:
                file_bytes = np.frombuffer(f.read(), np.uint8)
                loaded = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            
            if loaded is not None:
                print("✅ 中文文件名处理正常（使用编码方式）")
            else:
                print("❌ 中文文件名读取失败")
            
            os.remove(chinese_file)
        else:
            print("❌ 图片编码失败")
    except Exception as e:
        print(f"❌ 中文文件名测试失败: {e}")

def create_safe_image_handler():
    """创建安全的图片处理函数"""
    print("\n📝 创建安全的图片处理函数...")
    
    safe_handler_code = '''
def safe_cv2_imread(image_path):
    """安全的OpenCV图片读取函数，处理中文路径"""
    try:
        # 方法1：直接读取
        img = cv2.imread(image_path)
        if img is not None:
            return img
        
        # 方法2：使用字节流读取（处理中文路径）
        with open(image_path, 'rb') as f:
            file_bytes = np.frombuffer(f.read(), np.uint8)
            img = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            return img
    except Exception as e:
        print(f"读取图片失败 {image_path}: {e}")
        return None

def safe_cv2_imwrite(image_path, img):
    """安全的OpenCV图片保存函数，处理中文路径"""
    try:
        # 方法1：直接保存
        success = cv2.imwrite(image_path, img)
        if success:
            return True
        
        # 方法2：使用字节流保存（处理中文路径）
        success, encoded_img = cv2.imencode('.png', img)
        if success:
            with open(image_path, 'wb') as f:
                f.write(encoded_img.tobytes())
            return True
        return False
    except Exception as e:
        print(f"保存图片失败 {image_path}: {e}")
        return False
'''
    
    # 保存到文件
    with open('safe_image_utils.py', 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('# -*- coding: utf-8 -*-\n')
        f.write('"""\n安全的图片处理工具函数\n"""\n\n')
        f.write('import cv2\nimport numpy as np\n\n')
        f.write(safe_handler_code)
    
    print("✅ 已创建 safe_image_utils.py")

def clean_temp_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    temp_patterns = [
        "single_take_button.png",
        "multi_take_button.png", 
        "bottom_multi_take_button.png",
        "appearance_name.png",
        "appearance_name_bottom.png",
        "temp_processed_*.png",
        "lock.png",
        "login.png",
        "server.png",
        "logout.png",
        "goods.png",
    ]
    
    import glob
    cleaned_count = 0
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            try:
                os.remove(file_path)
                print(f"🗑️ 已清理: {os.path.basename(file_path)}")
                cleaned_count += 1
            except Exception as e:
                print(f"❌ 清理失败 {file_path}: {e}")
    
    print(f"📊 共清理了 {cleaned_count} 个临时文件")

def main():
    """主函数"""
    print("🔧 OpenCV编码问题修复工具")
    print("=" * 50)
    
    # 1. 测试OpenCV
    test_opencv_with_chinese_path()
    
    # 2. 创建安全处理函数
    create_safe_image_handler()
    
    # 3. 清理临时文件
    clean_temp_files()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    
    print("\n📝 解决方案:")
    print("1. ✅ 已将中文文件名改为英文文件名")
    print("2. ✅ 已创建安全的图片处理函数")
    print("3. ✅ 已清理可能有问题的临时文件")
    
    print("\n⚠️ 如果问题仍然存在:")
    print("- 检查系统编码设置")
    print("- 确保Python环境支持UTF-8")
    print("- 重启程序重新生成临时文件")

if __name__ == "__main__":
    main()
