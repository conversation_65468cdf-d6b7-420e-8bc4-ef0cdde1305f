#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI大模型OCR识别模块
支持多种AI服务进行图像文字识别
"""

import os
import base64
import json
import requests
import time
from typing import Optional, Dict, Any
import cv2
import numpy as np

class AIImageRecognizer:
    """AI图像识别器"""

    def __init__(self, config_file="ai_config.json"):
        """初始化AI识别器"""
        self.config = self.load_config(config_file)
        self.current_service = "qwen"  # 固定使用通义千问
        
    def load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "qwen": {
                "api_key": "",
                "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "model": "qwen-vl-plus",
                "enabled": True
            },
            "match_prompt": "请仔细观察图片中的游戏外观名称，判断是否与目标外观名称'{target_name}'完全匹配。请只回答'是'或'否'，不要添加任何其他内容。",
            "max_retries": 2,
            "retry_delay": 1.0
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"⚠️ 加载配置文件失败: {e}，使用默认配置")
        else:
            # 创建默认配置文件
            try:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=2)
                print(f"📝 已创建默认配置文件: {config_file}")
            except Exception as e:
                print(f"⚠️ 创建配置文件失败: {e}")
        
        return default_config
    
    def image_to_base64(self, image_path: str) -> Optional[str]:
        """将图片转换为base64编码"""
        try:
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                print(f"❌ 无法读取图片: {image_path}")
                return None
            
            # 图片预处理 - 提高识别准确率
            img = self.preprocess_image(img)
            
            # 编码为base64
            _, buffer = cv2.imencode('.png', img)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            return img_base64
            
        except Exception as e:
            print(f"❌ 图片编码失败: {e}")
            return None
    
    def preprocess_image(self, img: np.ndarray) -> np.ndarray:
        """图片预处理，提高AI识别准确率"""
        try:
            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # 去噪
            denoised = cv2.medianBlur(enhanced, 3)
            
            # 锐化
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # 转回BGR格式
            result = cv2.cvtColor(sharpened, cv2.COLOR_GRAY2BGR)
            
            return result
            
        except Exception as e:
            print(f"⚠️ 图片预处理失败: {e}")
            return img
    
    def recognize_with_openai(self, image_base64: str) -> Optional[str]:
        """使用OpenAI GPT-4V识别"""
        try:
            service_config = self.config["services"]["openai"]
            if not service_config.get("enabled", False):
                return None
            
            headers = {
                "Authorization": f"Bearer {service_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": service_config["model"],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.config["recognition_prompt"]
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 100
            }
            
            response = requests.post(
                f"{service_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"].strip()
                return content
            else:
                print(f"❌ OpenAI API错误: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ OpenAI识别失败: {e}")
            return None
    
    def recognize_with_claude(self, image_base64: str) -> Optional[str]:
        """使用Claude识别"""
        try:
            service_config = self.config["services"]["claude"]
            if not service_config.get("enabled", False):
                return None
            
            headers = {
                "x-api-key": service_config["api_key"],
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            payload = {
                "model": service_config["model"],
                "max_tokens": 100,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.config["recognition_prompt"]
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": image_base64
                                }
                            }
                        ]
                    }
                ]
            }
            
            response = requests.post(
                f"{service_config['base_url']}/v1/messages",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["content"][0]["text"].strip()
                return content
            else:
                print(f"❌ Claude API错误: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Claude识别失败: {e}")
            return None
    
    def recognize_with_local(self, image_base64: str) -> Optional[str]:
        """使用本地Ollama模型识别"""
        try:
            service_config = self.config["services"]["local"]
            if not service_config.get("enabled", False):
                return None
            
            payload = {
                "model": service_config["model"],
                "prompt": self.config["recognition_prompt"],
                "images": [image_base64],
                "stream": False
            }
            
            response = requests.post(
                f"{service_config['base_url']}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("response", "").strip()
                return content
            else:
                print(f"❌ 本地模型API错误: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 本地模型识别失败: {e}")
            return None

    def match_appearance(self, image_path: str, target_name: str) -> bool:
        """使用AI判断图片中的外观是否匹配目标名称"""
        print(f"🤖 使用通义千问AI判断外观匹配: {image_path} vs {target_name}")

        # 转换图片为base64
        image_base64 = self.image_to_base64(image_path)
        if not image_base64:
            print("❌ 图片处理失败")
            return False

        # 检查通义千问配置
        if not self.config["qwen"].get("enabled", False):
            print("❌ 通义千问服务未启用")
            return False

        if not self.config["qwen"].get("api_key"):
            print("❌ 通义千问API密钥未配置")
            return False

        # 尝试匹配判断
        for attempt in range(self.config["max_retries"]):
            try:
                result = self.match_with_qwen(image_base64, target_name)

                if result is not None:
                    print(f"✅ 通义千问判断结果: {'匹配' if result else '不匹配'}")
                    return result

            except Exception as e:
                print(f"⚠️ 第{attempt+1}次尝试失败: {e}")

            if attempt < self.config["max_retries"] - 1:
                time.sleep(self.config["retry_delay"])

        print("❌ 通义千问判断失败")
        return False

    def match_with_qwen(self, image_base64: str, target_name: str) -> Optional[bool]:
        """使用通义千问判断外观匹配"""
        try:
            qwen_config = self.config["qwen"]

            headers = {
                "Authorization": f"Bearer {qwen_config['api_key']}",
                "Content-Type": "application/json"
            }

            # 构建匹配提示词
            match_prompt = self.config["match_prompt"].format(target_name=target_name)

            payload = {
                "model": qwen_config["model"],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": match_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 10,  # 只需要回答"是"或"否"
                "temperature": 0.1  # 降低随机性，提高一致性
            }

            response = requests.post(
                f"{qwen_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            
            print(f"HTTP状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 通义千wen API响应: {result}")
                content = result["choices"][0]["message"]["content"].strip()

                # 解析AI回答
                if "是" in content or "匹配" in content or "正确" in content:
                    return True
                elif "否" in content or "不匹配" in content or "错误" in content:
                    return False
                else:
                    print(f"⚠️ AI回答不明确: {content}")
                    return None

            else:
                print(f"❌ 通义千问API错误: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ 通义千问匹配失败: {e}")
            return None

    def clean_recognition_result(self, text: str) -> str:
        """清理识别结果"""
        if not text:
            return ""

        # 移除常见的无关字符
        text = text.strip()

        # 移除引号
        text = text.replace('"', '').replace("'", '').replace('"', '').replace('"', '')

        # 移除常见的OCR错误字符
        text = text.replace('|', '').replace('_', '').replace('-', '')

        # 移除多余的空格
        text = ' '.join(text.split())

        # 只保留中文、数字、常见符号
        import re
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s·（）()【】\[\]《》<>]', '', text)

        return text.strip()

    def test_services(self) -> Dict[str, bool]:
        """测试各个AI服务的可用性"""
        print("🧪 测试AI服务可用性...")

        # 创建测试图片
        test_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_img, "测试文字", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.imwrite("test_ai_ocr.png", test_img)

        results = {}

        for service_name in ["local", "openai", "claude", "qwen"]:
            if not self.config["services"][service_name].get("enabled", False):
                results[service_name] = False
                print(f"⚪ {service_name}: 未启用")
                continue

            try:
                # 简单测试
                image_base64 = self.image_to_base64("test_ai_ocr.png")
                if image_base64:
                    if service_name == "local":
                        result = self.recognize_with_local(image_base64)
                    elif service_name == "openai":
                        result = self.recognize_with_openai(image_base64)
                    elif service_name == "claude":
                        result = self.recognize_with_claude(image_base64)
                    elif service_name == "qwen":
                        result = self.recognize_with_qwen(image_base64)

                    if result and "测试" in result:
                        results[service_name] = True
                        print(f"✅ {service_name}: 可用")
                    else:
                        results[service_name] = False
                        print(f"❌ {service_name}: 识别失败")
                else:
                    results[service_name] = False
                    print(f"❌ {service_name}: 图片处理失败")

            except Exception as e:
                results[service_name] = False
                print(f"❌ {service_name}: 测试失败 - {e}")

        # 清理测试文件
        try:
            os.remove("test_ai_ocr.png")
        except:
            pass

        return results


# 全局AI识别器实例
_ai_recognizer = None

def get_ai_recognizer() -> AIImageRecognizer:
    """获取AI识别器实例（单例模式）"""
    global _ai_recognizer
    if _ai_recognizer is None:
        _ai_recognizer = AIImageRecognizer()
    return _ai_recognizer

def ai_match_appearance(image_path: str, target_name: str) -> bool:
    """便捷函数：使用AI判断外观匹配"""
    recognizer = get_ai_recognizer()
    return recognizer.match_appearance(image_path, target_name)
