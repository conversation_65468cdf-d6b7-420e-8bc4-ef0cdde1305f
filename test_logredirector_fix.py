#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LogRedirector修复验证脚本
"""

def test_logredirector_import():
    """测试LogRedirector类是否可以正常导入"""
    print("🧪 LogRedirector导入测试")
    print("=" * 40)
    
    try:
        from gui import LogRedirector
        print("✅ LogRedirector类导入成功")
        
        # 创建一个模拟的GUI对象
        class MockGUI:
            def __init__(self):
                self.logs = []
            
            def log(self, message):
                self.logs.append(message)
                print(f"📝 日志: {message}")
        
        # 测试LogRedirector功能
        mock_gui = MockGUI()
        redirector = LogRedirector(mock_gui)
        
        print("✅ LogRedirector实例创建成功")
        
        # 测试write方法
        redirector.write("测试消息1")
        redirector.write("测试消息2\n")
        redirector.write("")  # 空消息应该被忽略
        redirector.write("   ")  # 空白消息应该被忽略
        redirector.write("测试消息3")
        
        # 测试flush方法
        redirector.flush()
        print("✅ flush方法调用成功")
        
        # 验证日志记录
        expected_logs = ["测试消息1", "测试消息2", "测试消息3"]
        if mock_gui.logs == expected_logs:
            print("✅ 日志记录功能正常")
            print(f"   记录的日志: {mock_gui.logs}")
        else:
            print("❌ 日志记录功能异常")
            print(f"   期望: {expected_logs}")
            print(f"   实际: {mock_gui.logs}")
        
        return True
        
    except ImportError as e:
        print(f"❌ LogRedirector导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ LogRedirector测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ GUI集成测试")
    print("=" * 30)
    
    try:
        # 检查GUI文件中是否只有一个LogRedirector定义
        with open("gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 统计LogRedirector类定义的数量
        class_count = content.count("class LogRedirector:")
        
        if class_count == 1:
            print("✅ GUI文件中只有一个LogRedirector类定义")
        else:
            print(f"❌ GUI文件中有 {class_count} 个LogRedirector类定义")
            return False
        
        # 检查是否在文件顶部定义
        lines = content.split('\n')
        logredirector_line = -1
        for i, line in enumerate(lines):
            if "class LogRedirector:" in line:
                logredirector_line = i + 1
                break
        
        if logredirector_line > 0 and logredirector_line < 30:
            print(f"✅ LogRedirector类在第{logredirector_line}行定义（文件顶部）")
        else:
            print(f"⚠️ LogRedirector类在第{logredirector_line}行定义")
        
        # 检查是否有重复的内部定义
        method_definitions = content.count("class LogRedirector:")
        if method_definitions == 1:
            print("✅ 没有重复的LogRedirector定义")
        else:
            print(f"❌ 发现 {method_definitions} 个LogRedirector定义")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def test_multi_delivery_function():
    """测试多数量发货功能是否可以正常调用"""
    print("\n🔢 多数量发货功能测试")
    print("=" * 30)
    
    try:
        from gui import DeliveryGUI
        print("✅ DeliveryGUI类导入成功")
        
        # 检查是否有start_multi_delivery方法
        if hasattr(DeliveryGUI, 'start_multi_delivery'):
            print("✅ start_multi_delivery方法存在")
        else:
            print("❌ start_multi_delivery方法不存在")
            return False
        
        # 检查是否有execute_multiple_delivery方法
        from dm_main import GameScript
        if hasattr(GameScript, 'execute_multiple_delivery'):
            print("✅ execute_multiple_delivery方法存在")
        else:
            print("❌ execute_multiple_delivery方法不存在")
            return False
        
        # 检查是否有find_and_take_single_item方法
        if hasattr(GameScript, 'find_and_take_single_item'):
            print("✅ find_and_take_single_item方法存在")
        else:
            print("❌ find_and_take_single_item方法不存在")
            return False
        
        print("✅ 所有必要的方法都存在")
        return True
        
    except Exception as e:
        print(f"❌ 多数量发货功能测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 LogRedirector修复总结")
    print("=" * 40)
    print("🔧 修复内容:")
    print("1. 将LogRedirector类提取到文件顶部")
    print("2. 删除了方法内部的重复定义")
    print("3. 确保全局可访问性")
    print()
    print("🎯 修复前的问题:")
    print("- LogRedirector在方法内部定义")
    print("- 存在多个重复定义")
    print("- 导致 'name LogRedirector is not defined' 错误")
    print()
    print("✅ 修复后的改进:")
    print("- LogRedirector作为独立类在文件顶部定义")
    print("- 全局可访问，避免命名错误")
    print("- 代码结构更清晰")

def main():
    """主函数"""
    print("🔧 LogRedirector修复验证工具")
    print("=" * 50)
    
    # 运行各种测试
    test1_success = test_logredirector_import()
    test2_success = test_gui_integration()
    test3_success = test_multi_delivery_function()
    
    # 显示修复总结
    show_fix_summary()
    
    # 总结测试结果
    print("\n📊 测试结果总结:")
    print("=" * 30)
    print(f"LogRedirector导入测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"GUI集成测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"多数量发货功能测试: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if all([test1_success, test2_success, test3_success]):
        print("\n🎉 所有测试通过！LogRedirector修复成功！")
        print("现在可以正常使用多数量发货功能了。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题。")

if __name__ == "__main__":
    main()
