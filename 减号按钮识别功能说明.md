# 减号按钮识别功能说明

## 🎯 功能概述

当AI识别到包含"取出数量"的界面时，除了识别取出按钮的坐标，还会同时识别减号按钮的坐标。系统会自动点击减号按钮将数量调整到1，然后再执行取出操作。

## ✨ 核心特性

### 🔍 智能识别
- **双按钮识别**: 同时识别取出按钮和减号按钮
- **类型判断**: 自动判断是否为"取出数量"界面
- **精确定位**: 返回两个按钮的精确坐标

### 🔢 数量调整
- **自动减量**: 多次点击减号按钮将数量调整到1
- **智能控制**: 最多点击10次，避免无限循环
- **状态反馈**: 详细的操作日志记录

## 🔧 技术实现

### AI提示词更新

#### 新的识别要求
```
如果发现"取出数量"相关的界面，还需要查找减号按钮：
- 减号按钮通常是"-"符号
- 位于数量输入框的左侧或附近
- 用于减少取出数量
```

#### 返回格式变化
```json
// 原来的格式（单个按钮）
{
  "found": true,
  "type": "single",
  "x": 900,
  "y": 520,
  "description": "找到取出按钮"
}

// 新的格式（包含减号按钮）
{
  "found": true,
  "type": "multiple",
  "take_button": {"x": 900, "y": 570},
  "minus_button": {"x": 820, "y": 470},
  "description": "找到取出按钮和减号按钮"
}
```

### 代码实现

#### 1. AI响应解析更新
```python
# 处理新的返回格式
if "take_button" in button_info:
    # 新格式：包含take_button和minus_button
    response = {
        "found": True,
        "type": button_info.get("type", "single"),
        "x": int(button_info["take_button"].get("x", -1)),
        "y": int(button_info["take_button"].get("y", -1)),
        "description": button_info.get("description", "")
    }
    
    # 如果是multiple类型且有minus_button，添加减号按钮坐标
    if button_info.get("type") == "multiple" and "minus_button" in button_info:
        response["minus_x"] = int(button_info["minus_button"].get("x", -1))
        response["minus_y"] = int(button_info["minus_button"].get("y", -1))
    
    return response
```

#### 2. 主程序处理逻辑
```python
# 根据按钮类型执行不同操作
if button_info['type'] == 'single':
    print("单个取出按钮，直接点击取出")
    # 直接点击取出按钮
    self.dm.MoveTo(button_info['x'], button_info['y'])
    self.dm.LeftClick()
    
elif button_info['type'] == 'multiple':
    print("多个取出按钮，需要先调整数量到1")
    
    # 检查是否有减号按钮坐标
    if 'minus_x' in button_info and 'minus_y' in button_info:
        # 多次点击减号按钮，将数量减到1
        for i in range(10):  # 最多点击10次减号
            self.dm.MoveTo(button_info['minus_x'], button_info['minus_y'])
            self.dm.LeftClick()
            time.sleep(0.2)
    
    # 点击取出按钮
    self.dm.MoveTo(button_info['x'], button_info['y'])
    self.dm.LeftClick()
```

## 📊 识别流程

### 界面类型判断
```
AI分析游戏截图
├─ 包含"取出数量"文字？
│  ├─ 是 → type="multiple"
│  │     ├─ 查找取出按钮坐标
│  │     ├─ 查找减号按钮坐标
│  │     └─ 返回双按钮信息
│  └─ 否 → type="single"
│        ├─ 查找取出按钮坐标
│        └─ 返回单按钮信息
```

### 操作执行流程
```
检查按钮类型
├─ type="single"
│  └─ 直接点击取出按钮
└─ type="multiple"
   ├─ 检查是否有减号按钮坐标
   ├─ 多次点击减号按钮(最多10次)
   ├─ 将数量调整到1
   └─ 点击取出按钮
```

## 🎮 实际应用场景

### 场景1: 单个取出界面
```
界面特征: 只有"取出"按钮，无数量选择
AI识别: type="single"
操作流程: 直接点击取出按钮
```

### 场景2: 数量选择界面
```
界面特征: 包含"取出数量"、减号按钮、加号按钮、数量输入框
AI识别: type="multiple"
操作流程: 点击减号按钮调整数量 → 点击取出按钮
```

## 📝 日志示例

### 单个取出按钮
```
✅ AI找到取出按钮: 类型=single, 坐标=(900, 520)
✓ 成功匹配外观: 测试外观
执行取出操作...
单个取出按钮，直接点击取出
```

### 多个取出按钮（包含减号）
```
✅ AI找到取出按钮: 类型=multiple, 坐标=(900, 570)
✓ 成功匹配外观: 测试外观
执行取出操作...
多个取出按钮，需要先调整数量到1
找到减号按钮，坐标: (820, 470)
点击减号按钮，将数量调整到1...
数量已调整，点击取出按钮
```

### 多个取出按钮（无减号）
```
✅ AI找到取出按钮: 类型=multiple, 坐标=(900, 570)
✓ 成功匹配外观: 测试外观
执行取出操作...
多个取出按钮，需要先调整数量到1
⚠️ 未找到减号按钮坐标，直接点击取出
```

## 🧪 测试验证

### 测试脚本
```bash
python test_minus_button.py
```

### 测试内容
- ✅ 创建包含取出数量界面的模拟截图
- ✅ AI识别减号按钮和取出按钮
- ✅ 验证坐标准确性
- ✅ 模拟点击流程测试

### 预期结果
```
📊 AI识别结果:
  找到按钮: True
  按钮类型: multiple
  取出按钮坐标: (900, 570)
  减号按钮坐标: (820, 470)
  ✅ 成功识别减号按钮！
  ✅ 减号按钮坐标准确
```

## 🛡️ 错误处理

### 容错机制
1. **减号按钮缺失**: 如果AI未识别到减号按钮，直接点击取出按钮
2. **坐标验证**: 检查坐标是否在合理范围内
3. **点击限制**: 最多点击10次减号，避免无限循环
4. **兼容性**: 保持对旧格式的兼容性

### 错误场景处理
```python
# 场景1: 未找到减号按钮
if 'minus_x' not in button_info:
    print("⚠️ 未找到减号按钮坐标，直接点击取出")
    # 直接点击取出按钮

# 场景2: 坐标异常
if button_info['minus_x'] < 0 or button_info['minus_y'] < 0:
    print("⚠️ 减号按钮坐标异常，跳过数量调整")
    # 直接点击取出按钮
```

## 💡 优化建议

### 识别准确率优化
1. **提示词优化**: 更详细地描述减号按钮特征
2. **图像预处理**: 提高截图质量和对比度
3. **多次验证**: 对关键坐标进行二次确认

### 操作效率优化
1. **智能减量**: 根据当前数量智能确定减号点击次数
2. **状态检测**: 实时检测数量变化，避免过度点击
3. **快速模式**: 对于已知界面类型，跳过AI识别

## ✅ 总结

🎉 **减号按钮识别功能已完成！**

### 主要特性
- ✅ **双按钮识别**: 同时识别取出按钮和减号按钮
- ✅ **智能调整**: 自动将数量调整到1
- ✅ **类型判断**: 准确区分单个/多个取出界面
- ✅ **容错处理**: 完善的错误处理机制

### 技术亮点
- 🔍 **AI增强**: 提升了AI识别的功能范围
- 🎯 **精确控制**: 精确的按钮坐标定位
- 🔄 **流程优化**: 自动化的数量调整流程
- 🛡️ **稳定可靠**: 多重容错保障

现在当遇到包含"取出数量"的界面时，AI会自动识别减号按钮并调整数量到1，确保每次只取出一个物品！🎯
