#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实游戏屏幕AI按钮查找测试
"""

import os
import sys
from ai_ocr import ai_find_take_button

def test_with_real_screenshot():
    """使用真实游戏截图测试AI按钮查找"""
    print("🎮 真实游戏截图AI按钮查找测试")
    print("=" * 50)
    
    # 检查配置
    try:
        from ai_ocr import get_ai_recognizer
        recognizer = get_ai_recognizer()
        
        if not recognizer.config["qwen"].get("api_key"):
            print("⚠️ 通义千问API密钥未配置")
            return False
        
        print("✅ 通义千问配置检查通过")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    print("\n📋 使用说明:")
    print("1. 请先启动游戏并打开物品栏界面")
    print("2. 确保界面中有'取出'按钮")
    print("3. 按任意键开始截图和AI识别...")
    
    input("按回车键继续...")
    
    try:
        # 模拟截取游戏屏幕（这里需要实际的大漠插件）
        print("\n📸 正在截取游戏屏幕...")
        
        # 如果有现成的截图文件，直接使用
        screenshot_files = [
            "game_screen.png",
            "screenshot.png", 
            "screen.png",
            "test_screen.png"
        ]
        
        screenshot_file = None
        for file in screenshot_files:
            if os.path.exists(file):
                screenshot_file = file
                print(f"✅ 找到截图文件: {file}")
                break
        
        if not screenshot_file:
            print("❌ 未找到游戏截图文件")
            print("请确保以下文件之一存在:")
            for file in screenshot_files:
                print(f"  - {file}")
            return False
        
        print(f"\n🔍 使用AI分析截图: {screenshot_file}")
        
        # 使用AI查找按钮
        result = ai_find_take_button(screenshot_file)
        
        print(f"\n📊 AI分析结果:")
        print("=" * 30)
        print(f"找到按钮: {result['found']}")
        
        if result['found']:
            print(f"按钮类型: {result['type']}")
            print(f"按钮坐标: ({result['x']}, {result['y']})")
            print(f"按钮描述: {result.get('description', '无')}")
            
            # 验证结果
            print(f"\n✅ 成功找到取出按钮！")
            print(f"🎯 可以在主程序中使用这些坐标进行点击操作")
            
            return True
        else:
            print(f"❌ 未找到取出按钮")
            print(f"💡 可能的原因:")
            print(f"  1. 当前界面没有取出按钮")
            print(f"  2. 按钮文字不是'取出'相关")
            print(f"  3. 按钮样式与预期不符")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def create_manual_test_guide():
    """创建手动测试指南"""
    print("\n📖 手动测试指南:")
    print("=" * 30)
    print("1. 准备工作:")
    print("   - 启动梦幻西游游戏")
    print("   - 登录角色")
    print("   - 打开物品栏或仓库界面")
    print("   - 确保界面中有'取出'按钮")
    print()
    print("2. 截图方法:")
    print("   - 使用QQ截图或其他工具")
    print("   - 保存为 game_screen.png")
    print("   - 放在当前目录下")
    print()
    print("3. 运行测试:")
    print("   python test_real_game_screen.py")
    print()
    print("4. 预期结果:")
    print("   - AI应该能找到按钮位置")
    print("   - 返回准确的坐标信息")
    print("   - 识别按钮类型（单个/多个）")

def interactive_test():
    """交互式测试"""
    print("\n🔧 交互式测试模式")
    print("=" * 30)
    
    while True:
        print("\n请选择操作:")
        print("1. 测试现有截图")
        print("2. 查看测试指南") 
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            success = test_with_real_screenshot()
            if success:
                print("\n🎉 测试成功！AI按钮查找功能正常工作")
            else:
                print("\n⚠️ 测试失败，请检查截图和界面")
                
        elif choice == '2':
            create_manual_test_guide()
            
        elif choice == '3':
            print("👋 退出测试")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🤖 真实游戏AI按钮查找测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ 缺少 requests 库，请运行: pip install requests")
        return
    
    # 检查是否有现成的截图
    screenshot_files = ["game_screen.png", "screenshot.png", "screen.png"]
    found_screenshots = [f for f in screenshot_files if os.path.exists(f)]
    
    if found_screenshots:
        print(f"📸 发现截图文件: {', '.join(found_screenshots)}")
        print("可以直接进行测试")
        
        # 直接测试
        success = test_with_real_screenshot()
        
        if success:
            print("\n🎉 AI按钮查找功能测试成功！")
            print("现在可以在主程序中使用此功能")
        else:
            print("\n💡 如果测试失败，可以尝试:")
            print("1. 确保截图包含清晰的取出按钮")
            print("2. 检查按钮文字是否为'取出'相关")
            print("3. 尝试不同的游戏界面截图")
    else:
        print("📸 未发现现成的截图文件")
        create_manual_test_guide()
        
        # 进入交互模式
        interactive_test()

if __name__ == "__main__":
    main()
