'''
必须是python32位版本
1.注册大漠
2.把对象赋值给全局大漠
3.正常使用大漠函数
'''
import os
import sys
import time
# 检查Python版本是否为32位
if sys.maxsize > 2**32:
    raise SystemError('必须使用32位Python版本！当前为64位Python')

import 大漠中文版 as dm

try:
    #1 先注册大漠
    dll = dm.注册大漠_简('lumiku2fdc744d96597f65888674a63fb3489a','yk38979202')
    #2 赋值对象给全局大漠
    dm.对象 = dll
    
    #3 获取当前文件所在目录并设置路径
    current_path = os.path.dirname(os.path.abspath(__file__))
    dm.置全局路径(current_path)
    

    # 点击账号输入框
    print("点击账号输入框...")
    dm.鼠标_鼠标移动点击(1089, 707)
    time.sleep(0.5)
    

    # 输入账号
    print("输入账号...")
    dm.键盘_字符串序列输入vip("123456",50)
    time.sleep(0.5)
    
except Exception as e:
    print(f'发生错误：{str(e)}')
