@echo off
chcp 65001 >nul
echo.
echo ========================================
echo           MQ功能控制面板
echo ========================================
echo.
echo 1. 启用MQ功能
echo 2. 禁用MQ功能  
echo 3. 查看当前状态
echo 4. 退出
echo.
set /p choice=请选择操作 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 正在启用MQ功能...
    python toggle_mq.py on
    pause
) else if "%choice%"=="2" (
    echo.
    echo 正在禁用MQ功能...
    python toggle_mq.py off
    pause
) else if "%choice%"=="3" (
    echo.
    echo 当前配置状态:
    python -c "from config import MQ_ENABLED; print('MQ功能:', '✅ 启用' if MQ_ENABLED else '❌ 禁用')"
    pause
) else if "%choice%"=="4" (
    echo 退出
    exit /b 0
) else (
    echo 无效选择，请重新运行
    pause
)
