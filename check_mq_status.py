#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQ状态检查脚本
"""

try:
    from config import MQ_ENABLED, RABBITMQ_CONFIG
    
    print("=" * 50)
    print("MQ功能状态检查")
    print("=" * 50)
    
    print(f"MQ功能开关: {'✅ 启用' if MQ_ENABLED else '❌ 禁用'}")
    print(f"队列名称: {RABBITMQ_CONFIG['queue_name']}")
    print(f"交换机名称: {RABBITMQ_CONFIG['exchange']}")
    print(f"路由键: {RABBITMQ_CONFIG['routing_key']}")
    
    if MQ_ENABLED:
        print("\n📝 当前状态: MQ功能已启用")
        print("   - 程序将连接到消息队列接收任务")
        print("   - 队列名使用正常的 'python'")
    else:
        print("\n📝 当前状态: MQ功能已禁用")
        print("   - 程序将进入待机模式，不接收MQ消息")
        print("   - 队列名使用禁用的 'python_disabled'")
        print("   - 要启用MQ功能，请运行: python toggle_mq.py on")
    
    print("\n🔧 控制命令:")
    print("   启用MQ: python toggle_mq.py on")
    print("   禁用MQ: python toggle_mq.py off")
    print("   或运行: mq_control.bat")
    
except ImportError as e:
    print(f"❌ 导入配置失败: {e}")
except Exception as e:
    print(f"❌ 检查状态失败: {e}")
