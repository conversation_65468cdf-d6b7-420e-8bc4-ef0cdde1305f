#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQ功能开关切换脚本
用于快速启用或禁用MQ消息队列功能
"""

import os
import sys

def read_config():
    """读取当前配置"""
    config_path = "config.py"
    if not os.path.exists(config_path):
        print("❌ 配置文件 config.py 不存在")
        return None
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    return content

def get_current_mq_status(content):
    """获取当前MQ状态"""
    for line in content.split('\n'):
        if line.strip().startswith('MQ_ENABLED'):
            if 'True' in line:
                return True
            elif 'False' in line:
                return False
    return None

def toggle_mq_status(content, new_status):
    """切换MQ状态"""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if line.strip().startswith('MQ_ENABLED'):
            # 替换MQ_ENABLED行
            indent = len(line) - len(line.lstrip())
            new_line = ' ' * indent + f'MQ_ENABLED = {new_status}  # {"启用" if new_status else "禁用"}MQ功能'
            new_lines.append(new_line)
        else:
            new_lines.append(line)
    
    return '\n'.join(new_lines)

def update_queue_names(content, enabled):
    """更新队列名称"""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if "'queue_name':" in line:
            if enabled:
                # 启用时使用正常队列名
                new_line = line.replace("'python_disabled'", "'python'")
            else:
                # 禁用时使用禁用队列名
                new_line = line.replace("'python'", "'python_disabled'")
            new_lines.append(new_line)
        elif "'exchange':" in line and "python" in line:
            if enabled:
                new_line = line.replace("'python_disabled'", "'python'")
            else:
                new_line = line.replace("'python'", "'python_disabled'")
            new_lines.append(new_line)
        elif "'routing_key':" in line and "python" in line:
            if enabled:
                new_line = line.replace("'python_disabled'", "'python'")
            else:
                new_line = line.replace("'python'", "'python_disabled'")
            new_lines.append(new_line)
        else:
            new_lines.append(line)
    
    return '\n'.join(new_lines)

def write_config(content):
    """写入配置文件"""
    config_path = "config.py"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)

def main():
    """主函数"""
    print("=" * 50)
    print("MQ功能开关切换工具")
    print("=" * 50)
    
    # 读取当前配置
    content = read_config()
    if content is None:
        return
    
    # 获取当前状态
    current_status = get_current_mq_status(content)
    if current_status is None:
        print("❌ 无法读取当前MQ状态")
        return
    
    print(f"当前MQ状态: {'✅ 启用' if current_status else '❌ 禁用'}")
    
    # 如果有命令行参数，直接设置
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['on', 'enable', 'true', '1', 'start']:
            new_status = True
        elif arg in ['off', 'disable', 'false', '0', 'stop']:
            new_status = False
        else:
            print("❌ 无效参数。使用 'on' 启用或 'off' 禁用")
            return
    else:
        # 交互式选择
        print("\n请选择操作:")
        print("1. 启用MQ功能")
        print("2. 禁用MQ功能")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            new_status = True
        elif choice == '2':
            new_status = False
        elif choice == '3':
            print("退出")
            return
        else:
            print("❌ 无效选择")
            return
    
    # 检查是否需要更改
    if current_status == new_status:
        print(f"✅ MQ功能已经是{'启用' if new_status else '禁用'}状态，无需更改")
        return
    
    try:
        # 更新配置
        print(f"🔄 正在{'启用' if new_status else '禁用'}MQ功能...")
        
        # 切换MQ状态
        new_content = toggle_mq_status(content, new_status)
        
        # 更新队列名称
        new_content = update_queue_names(new_content, new_status)
        
        # 写入文件
        write_config(new_content)
        
        print(f"✅ MQ功能已{'启用' if new_status else '禁用'}")
        
        if new_status:
            print("📝 提示: MQ功能已启用，程序将连接到消息队列接收任务")
            print("   - 队列名: python")
            print("   - 交换机: python")
            print("   - 路由键: python")
        else:
            print("📝 提示: MQ功能已禁用，程序将进入待机模式")
            print("   - 队列名: python_disabled")
            print("   - 交换机: python_disabled")
            print("   - 路由键: python_disabled")
        
        print("\n🔄 请重启程序使配置生效")
        
    except Exception as e:
        print(f"❌ 更新配置失败: {str(e)}")

if __name__ == "__main__":
    main()
