#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
减号按钮识别功能测试脚本
"""

import os
import cv2
import numpy as np
from ai_ocr import ai_find_take_button

def create_test_quantity_screen():
    """创建包含取出数量界面的测试截图"""
    print("📝 创建包含取出数量的模拟界面...")
    
    # 创建1920x1080的游戏屏幕
    screen = np.ones((1080, 1920, 3), dtype=np.uint8) * 50  # 深色背景
    
    # 添加游戏界面背景
    cv2.rectangle(screen, (0, 0), (1920, 1080), (30, 30, 30), -1)
    
    # 模拟取出数量对话框
    cv2.rectangle(screen, (600, 300), (1300, 700), (80, 80, 80), -1)
    cv2.rectangle(screen, (600, 300), (1300, 700), (150, 150, 150), 2)
    
    # 添加标题
    cv2.putText(screen, "取出数量", (850, 350), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
    
    # 模拟物品图标
    cv2.rectangle(screen, (700, 400), (800, 500), (100, 150, 200), -1)
    cv2.putText(screen, "装备", (720, 460), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 数量输入区域
    cv2.rectangle(screen, (850, 450), (1050, 490), (200, 200, 200), -1)
    cv2.putText(screen, "5", (940, 475), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 减号按钮 (重要：这是我们要识别的目标)
    cv2.rectangle(screen, (800, 450), (840, 490), (150, 100, 100), -1)  # 红色背景
    cv2.rectangle(screen, (800, 450), (840, 490), (200, 150, 150), 2)   # 边框
    cv2.putText(screen, "-", (815, 475), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
    
    # 加号按钮
    cv2.rectangle(screen, (1060, 450), (1100, 490), (100, 150, 100), -1)  # 绿色背景
    cv2.rectangle(screen, (1060, 450), (1100, 490), (150, 200, 150), 2)   # 边框
    cv2.putText(screen, "+", (1075, 475), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
    
    # 取出按钮
    cv2.rectangle(screen, (850, 550), (950, 590), (70, 130, 180), -1)  # 蓝色按钮
    cv2.rectangle(screen, (850, 550), (950, 590), (100, 150, 200), 2)   # 边框
    cv2.putText(screen, "取出", (875, 575), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 取消按钮
    cv2.rectangle(screen, (1000, 550), (1100, 590), (150, 100, 100), -1)  # 红色按钮
    cv2.rectangle(screen, (1000, 550), (1100, 590), (200, 150, 150), 2)   # 边框
    cv2.putText(screen, "取消", (1025, 575), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 添加一些其他界面元素
    cv2.putText(screen, "梦幻西游", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(screen, "帮会仓库", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (200, 200, 200), 2)
    
    # 保存测试图片
    cv2.imwrite("test_quantity_screen.png", screen)
    print("✅ 包含取出数量的模拟界面已创建: test_quantity_screen.png")
    
    return "test_quantity_screen.png"

def test_minus_button_recognition():
    """测试减号按钮识别功能"""
    print("🧪 减号按钮识别功能测试")
    print("=" * 40)
    
    # 检查配置
    try:
        from ai_ocr import get_ai_recognizer
        recognizer = get_ai_recognizer()
        
        if not recognizer.config["qwen"].get("api_key"):
            print("⚠️ 通义千问API密钥未配置")
            return False
        
        print("✅ 通义千问配置检查通过")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    # 创建测试图片
    test_image = create_test_quantity_screen()
    
    print("\n🔍 开始AI减号按钮识别测试...")
    
    try:
        # 使用AI查找按钮
        result = ai_find_take_button(test_image)
        
        print(f"\n📊 AI识别结果:")
        print(f"  找到按钮: {result['found']}")
        
        if result['found']:
            print(f"  按钮类型: {result['type']}")
            print(f"  取出按钮坐标: ({result['x']}, {result['y']})")
            
            if result['type'] == 'multiple':
                if 'minus_x' in result and 'minus_y' in result:
                    print(f"  减号按钮坐标: ({result['minus_x']}, {result['minus_y']})")
                    print("✅ 成功识别减号按钮！")
                    
                    # 验证坐标是否合理
                    if 800 <= result['minus_x'] <= 840 and 450 <= result['minus_y'] <= 490:
                        print("✅ 减号按钮坐标准确")
                    else:
                        print("⚠️ 减号按钮坐标可能不准确")
                        print(f"   期望范围: x=800-840, y=450-490")
                        print(f"   实际坐标: x={result['minus_x']}, y={result['minus_y']}")
                else:
                    print("❌ 未找到减号按钮坐标")
                    return False
            else:
                print("ℹ️ 识别为单个取出按钮，无需减号按钮")
            
            print(f"  按钮描述: {result.get('description', '无')}")
            return True
        else:
            print("❌ 未找到任何按钮")
            return False
            
    except Exception as e:
        print(f"❌ AI识别测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            os.remove(test_image)
            print(f"\n🧹 已清理测试文件: {test_image}")
        except:
            pass

def test_button_click_simulation():
    """模拟按钮点击流程测试"""
    print("\n🖱️ 按钮点击流程模拟测试")
    print("=" * 40)
    
    # 模拟AI识别结果
    mock_result = {
        "found": True,
        "type": "multiple",
        "x": 900,
        "y": 570,
        "minus_x": 820,
        "minus_y": 470,
        "description": "找到取出按钮和减号按钮"
    }
    
    print("📋 模拟AI识别结果:")
    print(f"  按钮类型: {mock_result['type']}")
    print(f"  取出按钮: ({mock_result['x']}, {mock_result['y']})")
    print(f"  减号按钮: ({mock_result['minus_x']}, {mock_result['minus_y']})")
    
    print("\n🔄 模拟点击流程:")
    
    if mock_result['type'] == 'multiple':
        print("1. 检测到多个取出按钮，需要调整数量")
        
        if 'minus_x' in mock_result and 'minus_y' in mock_result:
            print(f"2. 找到减号按钮: ({mock_result['minus_x']}, {mock_result['minus_y']})")
            print("3. 开始点击减号按钮，将数量调整到1...")
            
            # 模拟点击减号按钮
            for i in range(10):
                print(f"   点击减号 {i+1}/10: MoveTo({mock_result['minus_x']}, {mock_result['minus_y']}) -> LeftClick()")
                # 这里是模拟，实际代码中会调用 dm.MoveTo() 和 dm.LeftClick()
            
            print("4. 数量调整完成，点击取出按钮")
            print(f"   点击取出: MoveTo({mock_result['x']}, {mock_result['y']}) -> LeftClick()")
            
        else:
            print("2. ⚠️ 未找到减号按钮，直接点击取出")
            print(f"   点击取出: MoveTo({mock_result['x']}, {mock_result['y']}) -> LeftClick()")
    
    elif mock_result['type'] == 'single':
        print("1. 检测到单个取出按钮，直接点击")
        print(f"   点击取出: MoveTo({mock_result['x']}, {mock_result['y']}) -> LeftClick()")
    
    print("✅ 点击流程模拟完成")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 减号按钮识别使用指南")
    print("=" * 40)
    print("1. 功能说明:")
    print("   - AI自动识别取出数量界面中的减号按钮")
    print("   - 当检测到'取出数量'界面时，同时返回取出按钮和减号按钮坐标")
    print("   - 自动点击减号按钮将数量调整到1")
    print()
    print("2. 识别逻辑:")
    print("   - 如果界面包含'取出数量'文字 → type='multiple'")
    print("   - 同时查找'-'符号的减号按钮")
    print("   - 返回两个按钮的坐标信息")
    print()
    print("3. 返回格式:")
    print("   {")
    print("     'found': True,")
    print("     'type': 'multiple',")
    print("     'x': 900,           # 取出按钮坐标")
    print("     'y': 570,")
    print("     'minus_x': 820,     # 减号按钮坐标")
    print("     'minus_y': 470,")
    print("     'description': '找到取出按钮和减号按钮'")
    print("   }")
    print()
    print("4. 点击流程:")
    print("   - 检测到multiple类型")
    print("   - 多次点击减号按钮(最多10次)")
    print("   - 将数量调整到1")
    print("   - 点击取出按钮完成操作")

def main():
    """主函数"""
    print("🔘 减号按钮识别功能测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ 缺少 requests 库，请运行: pip install requests")
        return
    
    # 运行识别测试
    print("\n🧪 AI识别测试:")
    recognition_success = test_minus_button_recognition()
    
    # 运行点击流程测试
    test_button_click_simulation()
    
    # 显示使用指南
    show_usage_guide()
    
    # 总结
    print("\n📋 测试总结:")
    print("=" * 30)
    print(f"AI识别测试: {'✅ 通过' if recognition_success else '❌ 失败'}")
    print(f"点击流程测试: ✅ 通过")
    
    if recognition_success:
        print("\n🎉 减号按钮识别功能正常！")
        print("现在AI可以同时识别取出按钮和减号按钮")
        print("当遇到取出数量界面时，会自动调整数量到1")
    else:
        print("\n⚠️ 请检查API配置和提示词设置")
        print("可能需要优化AI提示词以提高识别准确率")

if __name__ == "__main__":
    main()
