#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的图片处理工具函数
"""

import cv2
import numpy as np


def safe_cv2_imread(image_path):
    """安全的OpenCV图片读取函数，处理中文路径"""
    try:
        # 方法1：直接读取
        img = cv2.imread(image_path)
        if img is not None:
            return img
        
        # 方法2：使用字节流读取（处理中文路径）
        with open(image_path, 'rb') as f:
            file_bytes = np.frombuffer(f.read(), np.uint8)
            img = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            return img
    except Exception as e:
        print(f"读取图片失败 {image_path}: {e}")
        return None

def safe_cv2_imwrite(image_path, img):
    """安全的OpenCV图片保存函数，处理中文路径"""
    try:
        # 方法1：直接保存
        success = cv2.imwrite(image_path, img)
        if success:
            return True
        
        # 方法2：使用字节流保存（处理中文路径）
        success, encoded_img = cv2.imencode('.png', img)
        if success:
            with open(image_path, 'wb') as f:
                f.write(encoded_img.tobytes())
            return True
        return False
    except Exception as e:
        print(f"保存图片失败 {image_path}: {e}")
        return False
