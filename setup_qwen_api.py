#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问API配置助手
"""

import json
import os
import requests

def test_qwen_api(api_key):
    """测试通义千问API是否有效"""
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 简单的API测试
        payload = {
            "model": "qwen-vl-plus",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "测试"
                        }
                    ]
                }
            ],
            "max_tokens": 5
        }
        
        response = requests.post(
            "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            return True, "API密钥有效"
        elif response.status_code == 401:
            return False, "API密钥无效或已过期"
        elif response.status_code == 429:
            return False, "API调用频率超限"
        else:
            return False, f"API错误: {response.status_code} - {response.text}"
            
    except Exception as e:
        return False, f"连接失败: {e}"

def update_config(api_key):
    """更新配置文件"""
    try:
        # 读取现有配置
        with open('ai_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新API密钥
        config['qwen']['api_key'] = api_key
        config['qwen']['enabled'] = True
        
        # 保存配置
        with open('ai_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 通义千问API配置助手")
    print("=" * 30)
    
    # 检查配置文件
    if not os.path.exists('ai_config.json'):
        print("❌ 配置文件不存在，请先运行主程序初始化")
        return
    
    print("📋 配置步骤:")
    print("1. 访问 https://dashscope.aliyun.com/")
    print("2. 注册/登录阿里云账号")
    print("3. 开通通义千问服务")
    print("4. 获取API密钥")
    print()
    
    # 获取API密钥
    api_key = input("请输入通义千问API密钥: ").strip()
    
    if not api_key:
        print("❌ API密钥不能为空")
        return
    
    if not api_key.startswith('sk-'):
        print("⚠️ API密钥格式可能不正确（通常以sk-开头）")
        confirm = input("是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            return
    
    print("\n🧪 测试API密钥...")
    
    # 测试API
    success, message = test_qwen_api(api_key)
    
    if success:
        print(f"✅ {message}")
        
        # 更新配置
        if update_config(api_key):
            print("✅ 配置已更新")
            
            print("\n🎉 配置完成！")
            print("现在可以使用通义千问进行外观匹配了")
            print()
            print("测试命令:")
            print("  python test_qwen_match.py")
            print()
            print("主程序中的变化:")
            print("  - 外观识别改为AI直接匹配判断")
            print("  - 更高的准确率，避免OCR错误")
            print("  - 失败时自动回退到传统OCR")
            
        else:
            print("❌ 配置更新失败")
    else:
        print(f"❌ {message}")
        print()
        print("💡 解决建议:")
        print("1. 检查API密钥是否正确")
        print("2. 确认账号余额充足")
        print("3. 检查网络连接")
        print("4. 确认已开通通义千问服务")

if __name__ == "__main__":
    main()
