#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文件名编码问题的脚本
清理可能存在编码问题的中文文件名图片文件
"""

import os
import glob
import shutil
from datetime import datetime

def clean_problematic_files():
    """清理可能存在编码问题的文件"""
    print("🧹 开始清理可能存在编码问题的文件...")
    
    # 当前目录
    current_dir = os.getcwd()
    print(f"📁 工作目录: {current_dir}")
    
    # 要清理的文件模式
    patterns_to_clean = [
        "*.png",  # 所有PNG文件
        "temp_*.png",  # 临时文件
        "*取出*.png",  # 包含"取出"的文件
        "*按钮*.png",  # 包含"按钮"的文件
        "*外观*.png",  # 包含"外观"的文件
    ]
    
    cleaned_files = []
    
    for pattern in patterns_to_clean:
        files = glob.glob(pattern)
        for file_path in files:
            try:
                # 检查文件是否存在编码问题
                file_name = os.path.basename(file_path)
                
                # 如果文件名包含中文或者看起来像乱码
                if any(ord(char) > 127 for char in file_name):
                    print(f"🗑️ 发现可能有编码问题的文件: {file_name}")
                    
                    # 备份文件（如果需要的话）
                    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file_name}"
                    
                    # 删除文件
                    os.remove(file_path)
                    cleaned_files.append(file_name)
                    print(f"✅ 已删除: {file_name}")
                    
            except Exception as e:
                print(f"❌ 处理文件 {file_path} 时出错: {e}")
    
    # 清理临时文件
    temp_patterns = [
        "temp_processed_*.png",
        "debug_*.png",
        "lock.png",
        "login.png", 
        "server.png",
        "logout.png",
        "goods.png",
    ]
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            try:
                os.remove(file_path)
                cleaned_files.append(os.path.basename(file_path))
                print(f"🗑️ 已清理临时文件: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ 清理临时文件 {file_path} 时出错: {e}")
    
    print(f"\n📊 清理完成，共处理了 {len(cleaned_files)} 个文件")
    
    if cleaned_files:
        print("🗑️ 已清理的文件:")
        for file_name in cleaned_files:
            print(f"   - {file_name}")
    else:
        print("✅ 没有发现需要清理的文件")

def check_opencv_support():
    """检查OpenCV支持情况"""
    print("\n🔍 检查OpenCV支持情况...")
    
    try:
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 测试基本功能
        import numpy as np
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # 测试保存和读取
        test_file = "test_opencv.png"
        cv2.imwrite(test_file, test_img)
        
        if os.path.exists(test_file):
            loaded_img = cv2.imread(test_file)
            if loaded_img is not None:
                print("✅ OpenCV读写功能正常")
                os.remove(test_file)
            else:
                print("❌ OpenCV读取功能异常")
        else:
            print("❌ OpenCV保存功能异常")
            
    except ImportError:
        print("❌ OpenCV未安装")
    except Exception as e:
        print(f"❌ OpenCV测试失败: {e}")

def create_safe_temp_dir():
    """创建安全的临时目录"""
    print("\n📁 创建安全的临时目录...")
    
    temp_dirs = ["temp", "debug_images", "png"]
    
    for dir_name in temp_dirs:
        if not os.path.exists(dir_name):
            try:
                os.makedirs(dir_name)
                print(f"✅ 创建目录: {dir_name}")
            except Exception as e:
                print(f"❌ 创建目录 {dir_name} 失败: {e}")
        else:
            print(f"📁 目录已存在: {dir_name}")

def main():
    """主函数"""
    print("🔧 文件名编码问题修复工具")
    print("=" * 50)
    
    # 1. 清理问题文件
    clean_problematic_files()
    
    # 2. 检查OpenCV
    check_opencv_support()
    
    # 3. 创建必要目录
    create_safe_temp_dir()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    print("\n📝 建议:")
    print("1. 重新运行程序测试功能")
    print("2. 如果仍有问题，请检查系统编码设置")
    print("3. 确保Python环境支持UTF-8编码")
    
    print("\n⚠️ 注意:")
    print("- 已清理的临时文件会在程序运行时重新生成")
    print("- 如果问题持续，可能需要检查系统区域设置")

if __name__ == "__main__":
    main()
