#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI按钮查找功能测试脚本
"""

import os
import cv2
import numpy as np
from ai_ocr import ai_find_take_button

def create_test_game_screen():
    """创建模拟游戏屏幕截图"""
    print("📝 创建更真实的模拟游戏屏幕...")

    # 创建1280x720的游戏屏幕（更常见的分辨率）
    screen = np.ones((720, 1280, 3), dtype=np.uint8) * 50  # 深色背景

    # 添加游戏界面背景
    cv2.rectangle(screen, (0, 0), (1280, 720), (30, 30, 30), -1)

    # 模拟游戏窗口
    cv2.rectangle(screen, (200, 100), (1000, 600), (80, 80, 80), -1)
    cv2.rectangle(screen, (200, 100), (1000, 600), (150, 150, 150), 2)

    # 添加窗口标题栏
    cv2.rectangle(screen, (200, 100), (1000, 130), (60, 60, 60), -1)
    cv2.putText(screen, "物品栏", (220, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    # 模拟物品格子
    for i in range(5):
        for j in range(8):
            x = 220 + j * 90
            y = 150 + i * 80
            cv2.rectangle(screen, (x, y), (x+70, y+60), (100, 100, 100), 1)

    # 模拟一个有物品的格子
    cv2.rectangle(screen, (310, 230), (380, 290), (150, 100, 50), -1)
    cv2.putText(screen, "装备", (325, 265), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    # 创建更真实的取出按钮 - 单个取出
    cv2.rectangle(screen, (850, 500), (950, 540), (70, 130, 180), -1)  # 蓝色按钮
    cv2.rectangle(screen, (850, 500), (950, 540), (100, 150, 200), 2)   # 边框
    cv2.putText(screen, "取出", (875, 525), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    # 创建多个取出按钮
    cv2.rectangle(screen, (750, 550), (880, 590), (180, 130, 70), -1)  # 橙色按钮
    cv2.rectangle(screen, (750, 550), (880, 590), (200, 150, 100), 2)   # 边框
    cv2.putText(screen, "全部取出", (760, 575), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

    # 添加一些其他界面元素
    cv2.putText(screen, "梦幻西游", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    # 保存测试图片
    cv2.imwrite("test_game_screen.png", screen)
    print("✅ 更真实的模拟游戏屏幕已创建: test_game_screen.png")

    return "test_game_screen.png"

def test_ai_button_finder():
    """测试AI按钮查找功能"""
    print("🧪 AI按钮查找功能测试")
    print("=" * 40)
    
    # 检查配置
    try:
        from ai_ocr import get_ai_recognizer
        recognizer = get_ai_recognizer()
        
        if not recognizer.config["qwen"].get("api_key"):
            print("⚠️ 通义千问API密钥未配置")
            print("请在 ai_config.json 中配置 qwen.api_key")
            return False
        
        print("✅ 通义千问配置检查通过")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    # 创建测试图片
    test_image = create_test_game_screen()
    
    print("\n🔍 开始AI按钮查找测试...")
    
    try:
        # 使用AI查找按钮
        result = ai_find_take_button(test_image)
        
        print(f"\n📊 AI查找结果:")
        print(f"  找到按钮: {result['found']}")
        
        if result['found']:
            print(f"  按钮类型: {result['type']}")
            print(f"  按钮坐标: ({result['x']}, {result['y']})")
            print(f"  按钮描述: {result.get('description', '无')}")
            
            # 验证坐标是否合理
            if 0 <= result['x'] <= 2000 and 0 <= result['y'] <= 2000:
                print("✅ 坐标范围正常")
            else:
                print("⚠️ 坐标超出屏幕范围")
            
            # 验证按钮类型
            if result['type'] in ['single', 'multiple']:
                print("✅ 按钮类型识别正确")
            else:
                print("⚠️ 按钮类型识别异常")
            
            return True
        else:
            print("❌ 未找到按钮")
            return False
            
    except Exception as e:
        print(f"❌ AI查找测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            os.remove(test_image)
            print(f"\n🧹 已清理测试文件: {test_image}")
        except:
            pass

def test_real_game_screen():
    """测试真实游戏屏幕（如果存在）"""
    print("\n🎮 测试真实游戏屏幕...")
    
    # 检查是否有真实的游戏截图
    real_screen_files = ["game_screen.png", "screenshot.png", "screen.png"]
    
    for screen_file in real_screen_files:
        if os.path.exists(screen_file):
            print(f"📸 发现游戏截图: {screen_file}")
            
            try:
                result = ai_find_take_button(screen_file)
                
                print(f"🔍 真实屏幕查找结果:")
                print(f"  找到按钮: {result['found']}")
                
                if result['found']:
                    print(f"  按钮类型: {result['type']}")
                    print(f"  按钮坐标: ({result['x']}, {result['y']})")
                    print(f"  按钮描述: {result.get('description', '无')}")
                    return True
                else:
                    print("  未在真实屏幕中找到按钮")
                    
            except Exception as e:
                print(f"❌ 真实屏幕测试失败: {e}")
    
    print("ℹ️ 未找到真实游戏截图文件")
    return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 AI按钮查找使用指南:")
    print("=" * 40)
    print("1. 功能说明:")
    print("   - AI自动在游戏屏幕中查找取出按钮")
    print("   - 识别按钮类型：单个取出 vs 多个取出")
    print("   - 返回按钮的精确坐标")
    print()
    print("2. 使用方法:")
    print("   from ai_ocr import ai_find_take_button")
    print("   result = ai_find_take_button('game_screen.png')")
    print()
    print("3. 返回格式:")
    print("   {")
    print("     'found': True/False,")
    print("     'type': 'single'/'multiple',")
    print("     'x': 按钮X坐标,")
    print("     'y': 按钮Y坐标,")
    print("     'description': '按钮描述'")
    print("   }")
    print()
    print("4. 集成到主程序:")
    print("   - 替代原有的FindPic方式")
    print("   - 一次性识别所有类型的按钮")
    print("   - 更高的准确率和灵活性")

def main():
    """主函数"""
    print("🤖 AI按钮查找功能测试工具")
    print("=" * 50)
    
    # 检查依赖
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ 缺少 requests 库，请运行: pip install requests")
        return
    
    # 运行模拟测试
    print("\n🧪 模拟测试:")
    mock_success = test_ai_button_finder()
    
    # 运行真实测试
    print("\n🎮 真实测试:")
    real_success = test_real_game_screen()
    
    # 显示使用指南
    show_usage_guide()
    
    # 总结
    print("\n📋 测试总结:")
    print("=" * 30)
    print(f"模拟测试: {'✅ 通过' if mock_success else '❌ 失败'}")
    print(f"真实测试: {'✅ 通过' if real_success else 'ℹ️ 跳过'}")
    
    if mock_success:
        print("\n🎉 AI按钮查找功能正常！")
        print("现在可以在主程序中使用AI查找按钮功能")
        print("这将大大提高按钮识别的准确率和灵活性")
    else:
        print("\n⚠️ 请检查API配置和网络连接")

if __name__ == "__main__":
    main()
