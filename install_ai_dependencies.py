#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI识别依赖安装脚本
"""

import subprocess
import sys
import os
import json

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_ai_dependencies():
    """安装AI识别相关依赖"""
    print("🚀 开始安装AI识别依赖...")
    
    # 必需的包
    required_packages = [
        "requests",
        "opencv-python", 
        "numpy",
        "Pillow"
    ]
    
    # 可选的包（用于更好的性能）
    optional_packages = [
        "opencv-contrib-python"
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    # 安装必需包
    for package in required_packages:
        if check_package(package.replace("-", "_")):
            print(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package):
                success_count += 1
    
    # 安装可选包
    for package in optional_packages:
        if not check_package(package.replace("-", "_")):
            install_package(package)
    
    print(f"\n📊 必需依赖安装结果: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✅ 所有必需依赖安装成功！")
        return True
    else:
        print("❌ 部分依赖安装失败，请手动安装")
        return False

def setup_ollama_guide():
    """显示Ollama安装指南"""
    print("\n🦙 Ollama本地模型安装指南:")
    print("=" * 50)
    print("1. 下载Ollama:")
    print("   Windows: https://ollama.ai/download/windows")
    print("   macOS: https://ollama.ai/download/mac")
    print("   Linux: https://ollama.ai/download/linux")
    print()
    print("2. 安装完成后，打开命令行运行:")
    print("   ollama pull llava")
    print("   (这会下载约4GB的模型文件)")
    print()
    print("3. 启动Ollama服务:")
    print("   ollama serve")
    print("   (保持这个窗口运行)")
    print()
    print("4. 测试是否正常:")
    print("   在浏览器访问: http://localhost:11434")
    print("   应该看到 'Ollama is running'")
    print("=" * 50)

def setup_api_keys_guide():
    """显示API密钥配置指南"""
    print("\n🔑 API密钥配置指南:")
    print("=" * 50)
    print("编辑 ai_config.json 文件，配置你的API密钥:")
    print()
    print("OpenAI GPT-4V:")
    print('  "openai": {')
    print('    "api_key": "你的OpenAI API密钥",')
    print('    "enabled": true')
    print('  }')
    print()
    print("Claude 3:")
    print('  "claude": {')
    print('    "api_key": "你的Claude API密钥",')
    print('    "enabled": true')
    print('  }')
    print()
    print("通义千问:")
    print('  "qwen": {')
    print('    "api_key": "你的通义千问API密钥",')
    print('    "enabled": true')
    print('  }')
    print("=" * 50)

def create_example_config():
    """创建示例配置文件"""
    config_file = "ai_config_example.json"
    
    example_config = {
        "default_service": "local",
        "services": {
            "openai": {
                "api_key": "sk-your-openai-api-key-here",
                "base_url": "https://api.openai.com/v1",
                "model": "gpt-4o-mini",
                "enabled": False,
                "description": "OpenAI GPT-4V，需要API密钥，识别准确率高"
            },
            "claude": {
                "api_key": "your-claude-api-key-here",
                "base_url": "https://api.anthropic.com",
                "model": "claude-3-haiku-20240307",
                "enabled": False,
                "description": "Claude 3，需要API密钥，识别准确率高"
            },
            "qwen": {
                "api_key": "your-qwen-api-key-here",
                "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "model": "qwen-vl-plus",
                "enabled": False,
                "description": "阿里通义千问，需要API密钥，对中文支持好"
            },
            "local": {
                "base_url": "http://localhost:11434",
                "model": "llava",
                "enabled": True,
                "description": "本地Ollama模型，免费但需要安装，速度较慢"
            }
        },
        "recognition_prompt": "请识别图片中的中文文字内容，这是一个游戏界面的外观名称。请准确识别每个汉字，只返回识别到的文字内容，不要添加任何解释、标点符号或格式。",
        "max_retries": 2,
        "retry_delay": 1.0,
        "image_preprocessing": {
            "enabled": True,
            "enhance_contrast": True,
            "denoise": True,
            "sharpen": True
        },
        "fallback_to_ocr": True,
        "debug_mode": False
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(example_config, f, ensure_ascii=False, indent=2)
        print(f"📝 已创建示例配置文件: {config_file}")
        print("   请复制并重命名为 ai_config.json，然后修改其中的API密钥")
    except Exception as e:
        print(f"❌ 创建示例配置文件失败: {e}")

def main():
    """主函数"""
    print("🔧 AI识别功能安装向导")
    print("=" * 40)
    
    # 安装Python依赖
    deps_ok = install_ai_dependencies()
    
    if not deps_ok:
        print("\n❌ 依赖安装失败，请手动安装必需的包")
        return
    
    # 创建示例配置
    create_example_config()
    
    # 显示安装指南
    print("\n🎯 下一步操作:")
    print("1. 选择一种AI服务进行配置:")
    print("   a) 本地模型 (推荐新手): 免费，但需要下载模型")
    print("   b) 云端API: 付费，但识别更准确更快")
    print()
    
    choice = input("请选择 (a/b): ").lower().strip()
    
    if choice == 'a':
        setup_ollama_guide()
    elif choice == 'b':
        setup_api_keys_guide()
    else:
        print("\n📖 两种方式的详细说明:")
        setup_ollama_guide()
        setup_api_keys_guide()
    
    print("\n✅ 安装完成后，运行以下命令测试:")
    print("   python test_ai_recognition.py")
    print("\n💡 提示: AI识别功能已集成到dm_main.py中")
    print("   外观名称识别将自动使用AI模型，失败时回退到OCR")

if __name__ == "__main__":
    main()
