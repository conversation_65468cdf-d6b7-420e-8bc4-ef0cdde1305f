'''
#=================================
模块为了照顾英文不好的同学,特意全部按照中文格式编写
创建时间:2023.09.21
更新时间:2023.09.21
作者:暖君
#=================================
'''

import ctypes
import os

from win32com.client import Dispatch

# =============================
对象 = None


# 免注册调用大漠方法2
def 注册大漠_简(注册码='', 附加码=''):
    print('正在初始化')
    #  通过调用DmReg.dll注册大漠 这样不会把dm.dll写到系统中，从而实现免注册
    patch = ctypes.windll.LoadLibrary(os.path.dirname(__file__) + './DmReg.dll')
    patch.SetDllPathW(os.path.dirname(__file__) + './dm.dll', 0)
    dm_主对象 = Dispatch('dm.dmsoft')  # 创建对象
    ver = dm_主对象.ver()
    print('免注册调用初始化成功 版本号为:', ver)
    # 注册大漠VIP
    if ver != '':
        reg = dm_主对象.reg(注册码, 附加码)
        if reg == 1:
            print("大漠vip注册成功")
            return dm_主对象
        else:
            print(f"大漠注册失败,错误代码: {reg}")

# =================================
def 创建大漠对象(要创建的个数):
    '''
	这里是应对多线程 创建多个对象.返回的是列表类型
	如:
	dll = dm.注册大漠_简('注册码','附加码')
objects = dm.创建大漠对象(3) #创建三个对象
for i in objects:
	id = dm.取大漠对象ID(i)
	print(i,id)
	:param 要创建的个数:
	:return: 返回对象列表
	'''
    dms = []
    a = 0
    for i in range(要创建的个数):
        dms.append(Dispatch('dm.dmsoft'))  # 创建对象)
        a += 1
    return dms


# ===========================
def 取创建的大漠对象总数():
    '''
	:param 对象:
	:return: 返回所有的大漠对象个数
	'''
    return 对象.GetDmCount()


# ============================
def 取大漠对象ID():
    '''
	:param 对象:
	:return: GetID返回当前大漠对象的ID值
	这个值对于每个对象是唯一存在的。可以用来判定两个大漠对象是否一致
	'''
    return 对象.GetID()


# ==================
def 窗口_查找(类名='', 标题=''):
    '''
	:param 对象: 必填
	:param 类名: 可空字符串
	:param 标题: 可空字符串
	:return: 返回窗口句柄
	'''
    return 对象.FindWindow(类名, 标题)


# =================
def 窗口_枚举(parent, title, class_name, filter):
    '''
	根据指定条件,枚举系统中符合条件的窗口
	:param parent:整形数: 获得的窗口句柄是该窗口的子窗口的窗口句柄,取0时为获得桌面句柄
	:param title:窗口标题. 此参数是模糊匹配.
	:param class_name:窗口类名. 此参数是模糊匹配.
	:param filter:1 : 匹配窗口标题,参数title有效
						2 : 匹配窗口类名,参数class_name有效.
						4 : 只匹配指定父窗口的第一层孩子窗口
						8 : 匹配父窗口为0的窗口,即顶级窗口
						16 : 匹配可见的窗口
						32 : 匹配出的窗口按照窗口打开顺序依次排列
						这些值可以相加,比如4+8+16就是类似于任务管理器中的窗口列表
	:return:根据指定条件,枚举系统中符合条件的窗口
	'''
    return 对象.EnumWindow(parent, title, class_name, filter)


# ==================================
def 窗口_绑定Ex(hwnd, display, mouse, keypad, public, mode=int):
    return 对象.BindWindowEx(hwnd, display, mouse, keypad, public, mode)


# =================
def 窗口_绑定(hwnd, display, mouse, keypad, mode=int):
    return 对象.BindWindow(hwnd, display, mouse, keypad, mode)


# =======================================
def 窗口_绑定判断(hwnd):
    '''
	判断窗口是否绑定
	:param 对象:
	:param hwnd:
	:return:
	'''
    return 对象.IsBind(hwnd)


# ==============================
def 窗口_解绑():
    return 对象.UnBindWindow()


# ================================
def 窗口_取鼠标所在窗口句柄():
    return 对象.GetMousePointWindow()


# ================
def 鼠标_模拟鼠标真实移动开关(开关, 间隔, 距离):
    return 对象.EnableRealMouse(开关, 间隔, 距离)


# ==================
def 鼠标_鼠标移动(x, y):
    return 对象.MoveTo(x, y)


# ==================
def 鼠标_鼠标右键单击():
    return 对象.RightClick()


# =========================
def 鼠标_鼠标左键单击():
    return 对象.LeftClick()


# ==============================
def 鼠标_鼠标移动点击(x, y):
    鼠标_鼠标移动(x, y)
    随机延时(10, 100)
    鼠标_鼠标左键单击()


# ===============================
def 键盘_按键(键码):
    '''
	:param 对象:
	:param 键码: 虚拟键码
	:return:
	'''
    return 对象.KeyPress(键码)


# ========================
def 键盘_字符串序列输入vip(字符串,延迟=100):
    '''
	根据指定的字符串序列，依次按顺序按下其中的字符
	:param 对象:
	:param 字符串:需要按下的字符串序列. 比如"1234","abcd","7389,1462"等
	:return:
	'''
    return 对象.KeyPressStr(字符串,延迟)


# =============================
def 键盘_文本输入(字符串):
    return 对象.KeyPressChar(字符串)


# ===========================================
def 键盘_按住_键码(键码):
    '''
	虚拟键码
	:param 键码:
	:return:
	'''
    return 对象.KeyDown(键码)


# ========================================
def 键盘_按住_字符(键码):
    '''
	字符串描述的键码. 大小写无所谓
	:param 键码:
	:return:
	'''
    return 对象.KeyDownChar(键码)


# ==========================================
def 键盘_弹起_键码(键码):
    return 对象.KeyUp(键码)


# ==================================
def 键盘_弹起_字符(键码):
    return 对象.KeyUpChar(键码)


# ==================================
def 鼠标_置前台鼠标模拟方式(模式):
    '''
	除了模式3,其他模式同时支持32位系统和64位系统.
	:param 模式: 0 正常模式(默认模式) 1 硬件模拟 2 硬件模拟2(ps2) 3 硬件模拟3
	:return:
	'''
    return 对象.SetSimMode(模式)


# ====================================
def 随机延时(最小, 最大):
    '''
	单位毫秒
	:param 对象:
	:param 最小:
	:param 最大:
	:return:
	'''
    return 对象.Delays(最小, 最大)


# ===============
def 图色_找图(x1, y1, x2, y2, pic_name, delta_color, sim, dir):
    return 对象.FindPic(x1, y1, x2, y2, pic_name, delta_color, sim, dir, '', '')


# ============================
def 图色_多点找色(x1, y1, x2, y2, first_color, offset_color, sim, dir):
    return 对象.FindMultiColor(x1, y1, x2, y2, first_color, offset_color, sim, dir, '', '')


# =================================
def 图色_取色_RGB(x, y):
    '''
	获取(x,y)的颜色,颜色返回格式"RRGGBB"
	:param x:
	:param y:
	:return:
	'''
    return 对象.GetColor(x, y)


# ======================
def 图色_取区域里指定颜色(x1, y1, x2, y2, color, sim, dir):
    '''
	查找指定区域内的所有颜色,颜色格式"RRGGBB-DRDGDB",
	:param x1:
	:param y1:
	:param x2:
	:param y2:
	:param color:
	:param sim:
	:param dir:
	:return:返回所有颜色信息的坐标值,然后通过GetResultCount等接口来解析 (由于内存限制,返回的颜色数量最多为1800个左右)
	'''
    return 对象.FindColorEx(x1, y1, x2, y2, color, sim, dir)


# ===================================
def 图色_取区域里的指定颜色返回序号_取颜色坐标(colors, colors_序号):
    return 对象.GetResultPos(colors, colors_序号)


# ==================================
def 截图(x1, y1, x2, y2, file):
    '''
	:param x1:
	:param y1:
	:param x2:
	:param y2:
	:param file: 保存的文件名,保存的地方一般为SetPath中设置的目录,也可以指定全路径名
	:return:
	'''
    return 对象.Capture(x1, y1, x2, y2, file)


# ==========================================
def 识字(x1, y1, x2, y2, color_format, sim):
    '''
	识别屏幕范围(x1,y1,x2,y2)内符合color_format的字符串,并且相似度为sim 取值0.1-1
	:param x1:
	:param y1:
	:param x2:
	:param y2:
	:param color_format:
	:param sim:
	:return:
	'''
    return 对象.Ocr(x1, y1, x2, y2, color_format, sim)


# =================================
def 找字_fastExS(x1, y1, x2, y2, string, color_format, sim):
    '''
	返回多个字符串的坐标
	此函数比FindStrExS要快很多，尤其是在字库很大时，或者模糊识别时，效果非常明显。
推荐使用此函数。

另外由于此函数是只识别待查找的字符，所以可能会有如下情况出现问题。

比如 字库中有"张和三" 一共3个字符数据，然后待识别区域里是"张和三",如果用FindStrExS查找
"张三"肯定是找不到的，但是用FindStrFastExS却可以找到，因为"和"这个字符没有列入查找计划中
所以，在使用此函数时，也要特别注意这一点。


	:param x1:
	:param y1:
	:param x2:
	:param y2:
	:param string:
	:param color_format:
	:param sim:
	:return:
	'''
    return 对象.FindStrFastExS(x1, y1, x2, y2, string, color_format, sim)


# ===========================
def 找字_fastS(x1, y1, x2, y2, string, color_format, sim, intX, intY):
    '''
	返回找到的字符串. 没找到的话返回长度为0的字符串.
	:param x1:
	:param y1:
	:param x2:
	:param y2:
	:param string:
	:param color_format:
	:param sim:
	:param intX:
	:param intY:
	:return:
	'''
    return 对象.FindStrFastS(x1, y1, x2, y2, string, color_format, sim, intX, intY)


# ================================
def 置全局路径(路径):
    return 对象.SetPath(路径)


# ==================
def 置字库路径(序号, 文件名):
    '''
	示例:
        dm_ret = dm.SetDict(0,"test.txt")
	:param 序号:
	:param 文件名:
	:return:
	'''
    return 对象.SetDict(序号, 文件名)


# ===============================
def 插件错误提示开关(开关=int):
    '''
	0表示不打开,1表示打开
	:param 开关:
	:return:
	'''
    return SetShowErrorMsg(开关)


# =========================
def 随机延时(min_s, max_s):
    return 对象.Delays(min_s, max_s)


# ===============================
def a星_取指定坐标最近坐标(all_pos, type, 指定x, 指定y):
    '''
	:param 指定y:
	:param 指定x:
	:param all_pos:
	:param ints:取值为0或者1 如果all_pos的内容是由FindPicEx,FindStrEx,FindStrFastEx,FindStrWithFontEx返回，那么取值为0
	如果all_pos的内容是由FindColorEx, FindMultiColorEx,FindColorBlockEx返回，那么取值为1
	:param x:
	:param y:
	:return:
	'''
    return 对象.FindNearestPos(all_pos, type, 指定x, 指定y)


# ===================================
def a星坐标排序(all_pos, type, x, y):
    '''
	根据部分Ex接口的返回值，然后对所有坐标根据对指定坐标的距离(或者指定X或者Y)进行从小到大的排序.
	注意:如果x为65535并且y为0时，那么排序的结果是仅仅对x坐标进行排序,如果y为65535并且x为0时，那么排序的结果是仅仅对y坐标进行排序.
	:param all_pos:
	:param type:
	:param x: 为0 则以y坐标排序
	:param y:为0 则以x坐标排序
	:return:
	'''
    return 对象.SortPosDistance(all_pos, type, x, y)


if __name__ == '__main__':
    print('此文件为方法模块,请外部调用,内部运行无意义')
    注册大漠_简()
