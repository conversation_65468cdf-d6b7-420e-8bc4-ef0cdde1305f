import mysql.connector
import redis
from config import MYSQL_CONFIG, REDIS_CONFIG

class DatabaseManager:
    def __init__(self):
        self.mysql_conn = None
        self.redis_conn = None

    def connect_mysql(self):
        try:
            self.mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
            print("MySQL数据库连接成功")
            return True
        except Exception as e:
            print(f"MySQL数据库连接失败: {str(e)}")
            return False

    def connect_redis(self):
        try:
            self.redis_conn = redis.Redis(**REDIS_CONFIG)
            self.redis_conn.ping()
            print("Redis数据库连接成功")
            return True
        except Exception as e:
            print(f"Redis数据库连接失败: {str(e)}")
            return False

    def close_connections(self):
        if self.mysql_conn:
            self.mysql_conn.close()
        if self.redis_conn:
            self.redis_conn.close() 