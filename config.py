import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取程序运行目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# 使用相对路径
IMAGE_DIR = os.path.join(BASE_DIR, 'images')
CONFIG_FILE = os.path.join(BASE_DIR, 'account_config.json')

# 数据库配置
MYSQL_CONFIG = {
    'host': 'rm-bp1m7j5m7u7rgrve5ao.mysql.rds.aliyuncs.com',
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': 'root',
    'password': 'wj5201314@',
    'database': 'waiguan'
}

# Redis配置
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', '*************'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'password': os.getenv('REDIS_PASSWORD', 'wj5201314'),
    'db': int(os.getenv('REDIS_DB', 0))
}

# MQ功能开关
MQ_ENABLED = False  # 禁用MQ功能

# RabbitMQ配置
RABBITMQ_CONFIG = {
    'host': '************',
    'port': 5672,
    'username': 'waiguan',
    'password': 'waiguan',
    'virtual_host': 'JCcccHost',
    'queue_name': 'python_disabled',  # MQ功能已禁用
    'exchange': 'python_disabled',    # MQ功能已禁用
    'routing_key': 'python_disabled', # MQ功能已禁用
    'java_update_queue': 'TestDirectQueue',
    'java_update_exchange': 'TestDirectExchange',
    'java_update_routing_key': 'TestDirectRouting'
}